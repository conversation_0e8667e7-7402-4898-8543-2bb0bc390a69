import { MigrationInterface, QueryRunner } from "typeorm";

export class addThinoWebField1699780278307 implements MigrationInterface {
    name = 'addThinoWebField1699780278307'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`thinoWebExpir\` date NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`thinoWebExpir\``);
    }

}
