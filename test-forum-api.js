// 简单的测试脚本来验证论坛API
const https = require('https');

// 测试配置
const API_BASE = 'http://localhost:3000/api/v1/forum';
const API_KEY = '86eccc5cc72866af3cc4de322fbed943';
const CLIENT_ID = '222';

// 测试函数
async function testForumAPI() {
    console.log('开始测试论坛API...\n');

    // 测试获取最新话题
    try {
        console.log('1. 测试获取最新话题...');
        const latestUrl = `${API_BASE}/latest?apiKey=${API_KEY}&clientId=${CLIENT_ID}`;
        console.log(`请求URL: ${latestUrl}`);
        
        const response = await fetch(latestUrl);
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ 获取最新话题成功');
            console.log(`返回话题数量: ${data.data?.topics?.length || 0}`);
        } else {
            console.log('❌ 获取最新话题失败');
            console.log('错误信息:', data);
        }
    } catch (error) {
        console.log('❌ 获取最新话题出错:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试获取当前用户信息
    try {
        console.log('2. 测试获取当前用户信息...');
        const currentUserUrl = `${API_BASE}/current-user?apiKey=${API_KEY}&clientId=${CLIENT_ID}`;
        console.log(`请求URL: ${currentUserUrl}`);
        
        const response = await fetch(currentUserUrl);
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ 获取当前用户信息成功');
            console.log(`用户名: ${data.data?.current_user?.username || '未登录'}`);
        } else {
            console.log('❌ 获取当前用户信息失败');
            console.log('错误信息:', data);
        }
    } catch (error) {
        console.log('❌ 获取当前用户信息出错:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试获取用户话题（不指定用户名，使用当前用户）
    try {
        console.log('3. 测试获取当前用户话题（不指定用户名）...');
        const myTopicsUrl = `${API_BASE}/topics/created-by?apiKey=${API_KEY}&clientId=${CLIENT_ID}`;
        console.log(`请求URL: ${myTopicsUrl}`);
        
        const response = await fetch(myTopicsUrl);
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ 获取当前用户话题成功');
            console.log(`使用的用户名: ${data.data?.username || '未知'}`);
            console.log(`返回话题数量: ${data.data?.topics?.length || 0}`);
        } else {
            console.log('❌ 获取当前用户话题失败');
            console.log('错误信息:', data);
        }
    } catch (error) {
        console.log('❌ 获取当前用户话题出错:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试获取分类
    try {
        console.log('4. 测试获取论坛分类...');
        const categoriesUrl = `${API_BASE}/categories?apiKey=${API_KEY}&clientId=${CLIENT_ID}`;
        console.log(`请求URL: ${categoriesUrl}`);
        
        const response = await fetch(categoriesUrl);
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ 获取论坛分类成功');
            console.log(`返回分类数量: ${data.data?.categories?.length || 0}`);
        } else {
            console.log('❌ 获取论坛分类失败');
            console.log('错误信息:', data);
        }
    } catch (error) {
        console.log('❌ 获取论坛分类出错:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试获取标签
    try {
        console.log('5. 测试获取论坛标签...');
        const tagsUrl = `${API_BASE}/tags?apiKey=${API_KEY}&clientId=${CLIENT_ID}`;
        console.log(`请求URL: ${tagsUrl}`);
        
        const response = await fetch(tagsUrl);
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ 获取论坛标签成功');
            console.log(`返回标签数量: ${data.data?.tags?.length || 0}`);
        } else {
            console.log('❌ 获取论坛标签失败');
            console.log('错误信息:', data);
        }
    } catch (error) {
        console.log('❌ 获取论坛标签出错:', error.message);
    }

    console.log('\n测试完成！');
}

// 运行测试
testForumAPI();
