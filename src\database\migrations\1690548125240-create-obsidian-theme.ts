import { MigrationInterface, QueryRunner } from "typeorm";

export class createObsidianTheme1690548125240 implements MigrationInterface {
    name = 'createObsidianTheme1690548125240'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`obsidian_theme\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`repo\` varchar(255) NOT NULL, \`author\` varchar(255) NULL, \`authorAvatar\` varchar(255) NULL, \`banner\` varchar(255) NULL, \`modes\` varchar(255) NULL, \`tags\` varchar(255) NULL, \`score\` int NULL, \`description\` text NULL, \`chineseDescription\` text NULL, \`version\` varchar(255) NOT NULL, \`updatedTime\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`downloadCount\` int NOT NULL DEFAULT '0', \`pkmerDownloadCount\` int NOT NULL DEFAULT '0', \`downloadUrl\` varchar(255) NOT NULL, \`source\` enum ('official', 'community') NOT NULL DEFAULT 'official', \`valid\` tinyint NOT NULL DEFAULT 1, INDEX \`IDX_1f6508ea6a2e45b067e19a4c12\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_1f6508ea6a2e45b067e19a4c12\` ON \`obsidian_theme\``);
        await queryRunner.query(`DROP TABLE \`obsidian_theme\``);
    }

}
