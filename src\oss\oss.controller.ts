import { Controller, DefaultValuePipe, Delete, Get, ParseIntPipe, Post, Param, Query, Request, UploadedFile, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiProperty, ApiTags } from '@nestjs/swagger';
import { OssService } from './oss.service';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor } from '@nestjs/platform-express';
import { infinityPagination } from 'src/utils/infinity-pagination';

@ApiTags('OSS')
@Controller({
    path: 'oss',
    version: '1',
})
@Controller('oss')
export class OssController {
    constructor(private readonly ossService: OssService) { }

    //上传图片到OSS
    @ApiOperation({ summary: '上传图片到OSS', description: '无限制' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('upload')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    async uploadFile(@UploadedFile() file, @Request() reqest) {
        console.log(file)
        const creator = reqest.user.id;
        return this.ossService.uploadFile(creator, file);
    }

    //根据用户id分页获取文件列表
    @ApiOperation({ summary: '根据用户id分页获取文件列表', description: '无限制' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('list')
    async getFileList(
        @Request() reqest,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number
    ) {

        const creator = reqest.user.id;
        return infinityPagination(
            await this.ossService.getFilesByCreator(creator, { page, limit }),
            { page, limit }
        )
    }


    //根据文件path删除文件
    @ApiBearerAuth()
    @ApiOperation({ summary: '根据文件id删除文件', description: '无限制' })
    @UseGuards(AuthGuard('jwt'))
    @Delete('delete/:id')
    async deleteFile(@Request() reqest, @Param('id') id: string) {
        const creator = reqest.user.id;
        return this.ossService.deleteFileById(creator, id);
    }

    @ApiBearerAuth()
    @ApiOperation({ summary: '上传用户头像', description: '最大5MB' })
    @UseGuards(AuthGuard('jwt'))
    @Post('userAvatar')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    async uploadUserAvatar(@UploadedFile() file, @Request() reqest) {
        const userId = reqest.user.id;
        return this.ossService.uploadUserAvatar(userId, file);
    }

}
