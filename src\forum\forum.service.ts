import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// 定义接口，以便更好地类型检查和代码提示
interface Tag {
    id?: number; // 假设标签可能有ID，如果API不返回，可以移除
    name: string;
    // 根据实际API返回，可以添加更多属性，如 'topic_count', 'is_staff', 'target_tag_id' 等
}

interface TagGroupApiResponse {
    id: number;
    name: string;
    tag_names: string[]; // 组内的标签名称列表
    parent_tag_name?: string[]; // 父级标签组名称 (如果存在层级关系)
    one_per_topic: boolean;
    permissions?: any; // 权限信息，根据需要细化
    // ... 其他可能的标签组属性
}
@Injectable()
export class ForumService {
    private readonly logger = new Logger(ForumService.name);
    private readonly forumUrl: string;

    constructor(private readonly configService: ConfigService) {
        // 从环境变量获取论坛URL，如果没有则使用默认值
        this.forumUrl = this.configService.get('forum.url') || 'https://forum.pkmer.net';
    }

    /**
     * 获取论坛最新话题
     */
    async getLatestTopics(apiKey: string, clientId: string) {
        try {
            const response = await fetch(`${this.forumUrl}/latest.json`, {
                headers: {
                    'User-Api-Key': apiKey,
                    'User-Api-Client-Id': clientId,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new HttpException(
                    `论坛API请求失败: ${response.status} ${response.statusText}`,
                    response.status
                );
            }

            const data = await response.json();
            
            // 只返回前10个话题
            return {
                success: true,
                data: {
                    topics: data.topic_list?.topics?.slice(0, 10) || []
                }
            };
        } catch (error) {
            this.logger.error('获取最新话题失败:', error);
            
            if (error instanceof HttpException) {
                throw error;
            }
            
            throw new HttpException(
                '获取论坛最新话题失败',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取用户创建的话题
     */
    async getTopicsByUser(apiKey: string, clientId: string, username: string) {
        try {
            const encodedUsername = encodeURIComponent(username);
            const response = await fetch(`${this.forumUrl}/topics/created-by/${encodedUsername}.json`, {
                headers: {
                    'User-Api-Key': apiKey,
                    'User-Api-Client-Id': clientId,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new HttpException(
                    `论坛API请求失败: ${response.status} ${response.statusText}`,
                    response.status
                );
            }

            const data = await response.json();
            
            // 只返回前10个话题
            return {
                success: true,
                data: {
                    topics: data.topic_list?.topics?.slice(0, 10) || []
                }
            };
        } catch (error) {
            this.logger.error(`获取用户 ${username} 的话题失败:`, error);
            
            if (error instanceof HttpException) {
                throw error;
            }
            
            throw new HttpException(
                '获取用户话题失败',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取论坛分类
     */
    async getCategories(apiKey: string, clientId: string) {
        try {
            const response = await fetch(`${this.forumUrl}/categories.json?include_subcategories=true`, {
                headers: {
                    'User-Api-Key': apiKey,
                    'User-Api-Client-Id': clientId,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new HttpException(
                    `论坛API请求失败: ${response.status} ${response.statusText}`,
                    response.status
                );
            }

            const data = await response.json();
            const categories: { id: number; name: string }[] = [];

            if (data && data.category_list && data.category_list.categories) {
                data.category_list.categories.forEach((category: any) => {
                    categories.push({ id: category.id, name: category.name });
                    
                    // 处理子分类
                    if (category.subcategory_list) {
                        category.subcategory_list.forEach((subcategory: any) => {
                            categories.push({ 
                                id: subcategory.id, 
                                name: `${category.name} > ${subcategory.name}` 
                            });
                        });
                    }
                });
            }

            return {
                success: true,
                data: {
                    categories
                }
            };
        } catch (error) {
            this.logger.error('获取论坛分类失败:', error);
            
            if (error instanceof HttpException) {
                throw error;
            }
            
            throw new HttpException(
                '获取论坛分类失败',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取论坛标签
     */
    async getTags(apiKey: string, clientId: string) {
        const headers = {
            'User-Api-Key': apiKey,
            'User-Api-Client-Id': clientId,
            'Content-Type': 'application/json',
        };
        try {
            // 1. 并行发送请求获取所有标签和标签组
            const [tagsResponse, tagGroupsResponse] = await Promise.all([
                fetch(`${this.forumUrl}/tags.json`, { headers }),
                fetch(`${this.forumUrl}/tag_groups.json`, { headers }),
            ]);

            // 2. 检查两个响应的HTTP状态
            if (!tagsResponse.ok) {
                throw new HttpException(
                    `获取标签列表失败: ${tagsResponse.status} ${tagsResponse.statusText}`,
                    tagsResponse.status
                );
            }
            if (!tagGroupsResponse.ok) {
                throw new HttpException(
                    `获取标签组列表失败: ${tagGroupsResponse.status} ${tagGroupsResponse.statusText}`,
                    tagGroupsResponse.status
                );
            }

            // 3. 解析JSON数据
            const tagsData = await tagsResponse.json();
            const tagGroupsData = await tagGroupsResponse.json();

            // 4. 收集所有标签名称到一个 Set 中进行去重
            const uniqueTagNames = new Set<string>();

            // 从 /tags.json 中添加标签名称
            if (tagsData && tagsData.tags) {
                tagsData.tags.forEach((tag: any) => {
                    if (typeof tag.name === 'string') {
                        uniqueTagNames.add(tag.name);
                    }
                });
            }

            // 从 /tag_groups.json 中添加标签组内的标签名称
            if (tagGroupsData && tagGroupsData.tag_groups) {
                tagGroupsData.tag_groups.forEach((group: any) => {
                    if (Array.isArray(group.tag_names)) {
                        group.tag_names.forEach((tagName: any) => {
                            if (typeof tagName === 'string') {
                                uniqueTagNames.add(tagName);
                            }
                        });
                    }
                });
            }

            // 5. 将 Set 转换回数组
            const allUniqueTags: string[] = Array.from(uniqueTagNames);

            return {
                success: true,
                data: {
                    tags: allUniqueTags,
                },
            };
        } catch (error: any) {
            this.logger.error('获取论坛标签失败:', error);

            if (error instanceof HttpException) {
                throw error;
            }

            // 捕获fetch可能抛出的网络错误或其他非HTTP错误
            throw new HttpException(
                '获取论坛标签失败 (网络或解析错误)',
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
}
