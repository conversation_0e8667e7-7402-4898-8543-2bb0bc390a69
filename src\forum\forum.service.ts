import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ForumService {
    private readonly logger = new Logger(ForumService.name);
    private readonly forumUrl: string;

    constructor(private readonly configService: ConfigService) {
        // 从环境变量获取论坛URL，如果没有则使用默认值
        this.forumUrl = this.configService.get('forum.url') || 'https://forum.pkmer.net';
    }

    /**
     * 获取论坛最新话题
     */
    async getLatestTopics(apiKey: string, clientId: string) {
        try {
            const response = await fetch(`${this.forumUrl}/latest.json`, {
                headers: {
                    'User-Api-Key': apiKey,
                    'User-Api-Client-Id': clientId,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new HttpException(
                    `论坛API请求失败: ${response.status} ${response.statusText}`,
                    response.status
                );
            }

            const data = await response.json();
            
            // 只返回前10个话题
            return {
                success: true,
                data: {
                    topics: data.topic_list?.topics?.slice(0, 10) || []
                }
            };
        } catch (error) {
            this.logger.error('获取最新话题失败:', error);
            
            if (error instanceof HttpException) {
                throw error;
            }
            
            throw new HttpException(
                '获取论坛最新话题失败',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取用户创建的话题
     */
    async getTopicsByUser(apiKey: string, clientId: string, username: string) {
        try {
            const encodedUsername = encodeURIComponent(username);
            const response = await fetch(`${this.forumUrl}/topics/created-by/${encodedUsername}.json`, {
                headers: {
                    'User-Api-Key': apiKey,
                    'User-Api-Client-Id': clientId,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new HttpException(
                    `论坛API请求失败: ${response.status} ${response.statusText}`,
                    response.status
                );
            }

            const data = await response.json();
            
            // 只返回前10个话题
            return {
                success: true,
                data: {
                    topics: data.topic_list?.topics?.slice(0, 10) || []
                }
            };
        } catch (error) {
            this.logger.error(`获取用户 ${username} 的话题失败:`, error);
            
            if (error instanceof HttpException) {
                throw error;
            }
            
            throw new HttpException(
                '获取用户话题失败',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取论坛分类
     */
    async getCategories(apiKey: string, clientId: string) {
        try {
            const response = await fetch(`${this.forumUrl}/categories.json?include_subcategories=true`, {
                headers: {
                    'User-Api-Key': apiKey,
                    'User-Api-Client-Id': clientId,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new HttpException(
                    `论坛API请求失败: ${response.status} ${response.statusText}`,
                    response.status
                );
            }

            const data = await response.json();
            const categories: { id: number; name: string }[] = [];

            if (data && data.category_list && data.category_list.categories) {
                data.category_list.categories.forEach((category: any) => {
                    categories.push({ id: category.id, name: category.name });
                    
                    // 处理子分类
                    if (category.subcategory_list) {
                        category.subcategory_list.forEach((subcategory: any) => {
                            categories.push({ 
                                id: subcategory.id, 
                                name: `${category.name} > ${subcategory.name}` 
                            });
                        });
                    }
                });
            }

            return {
                success: true,
                data: {
                    categories
                }
            };
        } catch (error) {
            this.logger.error('获取论坛分类失败:', error);
            
            if (error instanceof HttpException) {
                throw error;
            }
            
            throw new HttpException(
                '获取论坛分类失败',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 获取论坛标签
     */
    async getTags(apiKey: string, clientId: string) {
        try {
            const response = await fetch(`${this.forumUrl}/tags.json`, {
                headers: {
                    'User-Api-Key': apiKey,
                    'User-Api-Client-Id': clientId,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new HttpException(
                    `论坛API请求失败: ${response.status} ${response.statusText}`,
                    response.status
                );
            }

            const data = await response.json();
            const tags: { name: string }[] = [];

            if (data && data.tags) {
                data.tags.forEach((tag: any) => {
                    tags.push({ name: tag.name });
                });
            }

            return {
                success: true,
                data: {
                    tags
                }
            };
        } catch (error) {
            this.logger.error('获取论坛标签失败:', error);
            
            if (error instanceof HttpException) {
                throw error;
            }
            
            throw new HttpException(
                '获取论坛标签失败',
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
