import { MigrationInterface, QueryRunner } from "typeorm";

export class pluginRecordAddVersion1690461763320 implements MigrationInterface {
    name = 'pluginRecordAddVersion1690461763320'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`download_records\` ADD \`version\` varchar(255) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`download_records\` DROP COLUMN \`version\``);
    }

}
