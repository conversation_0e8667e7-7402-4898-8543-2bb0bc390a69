import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query, Response, UseGuards, Request } from '@nestjs/common';
import { WechatService } from './service/wechat.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { WechatMessage } from './interfaces/wechat.interface';
import * as xmlParser from 'fast-xml-parser';
import { ConfigService } from '@nestjs/config';
import { checkSignature, checkThinoHelperSignature } from './utils/checkSignature';
import { WechatThinoService } from './service/wechat-thino.service';
import { AuthGuard } from '@nestjs/passport';
import { MoonReaderService } from './service/thino-moonreader.service';
import { ThinoService } from './service/thino.service';

@ApiTags('WechatMp')
@Controller({
    path: 'wechatmp',
    version: '1',
})
export class WechatMpController {
    constructor(
        public service: WechatService,
        public thinoService: ThinoService,
        public wechatThinoService: WechatThinoService,
        public moonReaderService: MoonReaderService,
        private configService: ConfigService
    ) { }

    @ApiOperation({ summary: '微信公众号接口验证', description: '无限制' })
    @Get('subscribe')
    @HttpCode(HttpStatus.OK)
    public checkSignature(
        @Query('signature') signature: string,
        @Query('timestamp') timestamp: string,
        @Query('nonce') nonce: string,
        @Query('echostr') echostr: string,
    ) {
        return checkSignature(signature, timestamp, nonce, echostr, this.configService.get('wechatOpen.mpToken'));
    }

    // TODO: del this test url
    // @ApiOperation({ summary: '微信公众号 access token 获取测试' })
    // @Get("accesstoken")
    // @HttpCode(HttpStatus.OK)
    // public async getWechatMpAccessToken(
    // ) {
    //     return this.service.getWechatMpAccessToken();
    // }

    @ApiOperation({ summary: '微信公众号消息处理', description: '无限制' })
    @Post('subscribe')
    @HttpCode(HttpStatus.OK)
    public async wechatMpMessage(
        @Query('signature') signature: string,
        @Query('timestamp') timestamp: string,
        @Query('nonce') nonce: string,
        @Query('echostr') echostr: string,
        @Body() body: any,
        @Response() res: any
    ) {
        // 验证消息来自微信
        if (checkSignature(signature, timestamp, nonce, echostr, this.configService.get('wechatOpen.mpToken')) === echostr) {
            const parser = new xmlParser.XMLParser();
            let message: WechatMessage = parser.parse(body)['xml'];

            const builder = new xmlParser.XMLBuilder();
            const replyInfo = await this.service.wechatMpMessageService(message)
            if (replyInfo.xml.Content === "") {
                res.send('')
                return
            }

            let replayXmlData = await builder.build(replyInfo);
            res.type('text/xml');
            res.send(replayXmlData)
        } else {
            throw new Error('消息不是来自微信')
        }

    }

    @ApiOperation({ summary: '解绑订阅号', description: "解绑订阅号" })
    @Post('wechat/unbindSubscribe')
    @UseGuards(AuthGuard('jwt'))
    @ApiBearerAuth()
    @HttpCode(HttpStatus.OK)
    public async unbindSubscribe(@Request() request) {
        const userId = request.user.id
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户未登录',
            }
        }
        return await this.service.unbindWechatUser(userId)
    }

    @ApiOperation({ summary: '微信服务号验证', description: '无限制' })
    @Get('service')
    @HttpCode(HttpStatus.OK)
    public checkSignatureService(
        @Query('signature') signature: string,
        @Query('timestamp') timestamp: string,
        @Query('nonce') nonce: string,
        @Query('echostr') echostr: string,
    ) {
        return checkSignature(signature, timestamp, nonce, echostr, this.configService.get('wechatOpen.mpToken'));
    }

    @ApiOperation({ summary: '微信服务号消息处理', description: '无限制' })
    @Post('service')
    @HttpCode(HttpStatus.OK)
    public async wechatServiceMessage(
        @Query('signature') signature: string,
        @Query('timestamp') timestamp: string,
        @Query('nonce') nonce: string,
        @Query('echostr') echostr: string,
        @Body() body: any,
        @Response() res: any
    ) {
        // 验证消息来自微信
        if (checkSignature(signature, timestamp, nonce, echostr, this.configService.get('wechatOpen.mpToken')) === echostr) {
            const parser = new xmlParser.XMLParser();
            let message: WechatMessage = parser.parse(body)['xml'];

            const builder = new xmlParser.XMLBuilder();
            const replyInfo = await this.wechatThinoService.wechatMpMessageService(message)
            if (replyInfo.xml.Content === "") {
                res.send('')
                return
            }

            let replayXmlData = await builder.build(replyInfo);
            res.type('text/xml');
            res.send(replayXmlData)
        } else {
            throw new Error('消息不是来自微信')
        }

    }


    @ApiOperation({ summary: '绑定thino服务号', description: "绑定thino服务号" })
    @Post('wechat/bindService')
    @UseGuards(AuthGuard('jwt'))
    @ApiBearerAuth()
    @HttpCode(HttpStatus.OK)
    public async bindService(@Request() request) {
        const userId = request.user.id
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户未登录',
            }
        }
        return this.wechatThinoService.getUserBindServiceUrl(userId)
    }


    @ApiOperation({ summary: '微信小助手url验证', description: '无限制' })
    @Get('/wework/thino')
    @HttpCode(HttpStatus.OK)
    public checkThinoSignatureService(
        @Query('msg_signature') signature: string,
        @Query('timestamp') timestamp: string,
        @Query('nonce') nonce: string,
        @Query('echostr') echostr: string,
    ) {
        return checkThinoHelperSignature(signature, timestamp, nonce, echostr, this.configService.get('wework.thinoHelperToken'));
    }

    @ApiOperation({ summary: '微信小助手消息处理', description: '无限制' })
    @Post('/wework/thino')
    @HttpCode(HttpStatus.OK)
    public async wechatThinoServiceMessage(
        @Query('msg_signature') signature: string,
        @Query('timestamp') timestamp: string,
        @Query('nonce') nonce: string,
        @Query('echostr') echostr: string,
        @Body() body: any,
        @Response() res: any
    ) {
        const result =  await this.thinoService.handleThinoHelperMessage()
        res.send(result)
    }


    @ApiOperation({ summary: '静读天下高亮', description: "静读天下高亮" })
    @Post('moonreader/highlight')
    @HttpCode(HttpStatus.OK)
    public async highlights(@Request() request) {
        const token = request.headers.authorization.split(" ")[1]
        if (!token) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户未登录',
            }
        }
        return this.moonReaderService.addHighlight(token, request.body.highlights)
    }

    @ApiOperation({ summary: '获取会话存储内容', description: "获取会话存储内容" })
    @Post('wechat/getWeworkMessage')
    @HttpCode(HttpStatus.OK)
    public async getWeworkMessage(@Body() body: any) {
        if (!body.secret) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "No secret"
            }
        }

        if (body.secret !== this.configService.get("wework.weworkApiSecret")) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "secret error"
            }
        }

        const response = await fetch("http://127.0.0.1:9000/get_chat_data", {
            method: "post",
            body: JSON.stringify(body),
        })

        const result = await response.json()
        return result
    }

    @ApiOperation({ summary: '获取会话存储媒体消息', description: "获取会话存储媒体消息" })
    @Post('wechat/getWeworkMedia')
    @HttpCode(HttpStatus.OK)
    public async getWeworkMedia(@Body() body: any) {
        if (!body.secret) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "No secret"
            }
        }

        if (body.secret !== this.configService.get("wework.weworkApiSecret")) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "secret error"
            }

        }

        const response = await fetch("http://127.0.0.1:9000/get_media_data", {
            method: "post",
            body: JSON.stringify(body),
        })

        const result = await response.json()
        return result
    }
}
