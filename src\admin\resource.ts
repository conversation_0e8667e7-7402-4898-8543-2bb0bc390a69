import { ResourceWithOptions } from "adminjs";
import { User } from "src/users/entities/user.entity";
import { ObsidianPlugin } from "src/download/entities/obsidian_plugin.entity";
import { Order } from "src/orders/entities/order.entity";
import { CheckoutOrder } from "src/orders/entities/checkout-order.entity";
import { ActivationRecord } from "src/website/entities/activation-record.entity";

export const userResource: ResourceWithOptions = {
    resource: User,
    options: {
        id: "用户设置",
        listProperties: ['id', 'nickName', 'email', 'phone', 'type', 'expirationDate', 'supporter', 'rewards', 'createdAt'],
        editProperties: ['nickName', 'type', 'expirationDate', 'supporter', 'rewards'],
        filterProperties: ['id', 'nickName', 'email', 'phone', 'type', 'expirationDate', 'supporter', 'rewards'],
        sort: {
            direction: 'desc',
            sortBy: 'createdAt',
        }
    }
}

export const pluginResource: ResourceWithOptions = {
    resource: ObsidianPlugin,
    options: {
        id: 'Obsidian插件设置',
        listProperties: ['id', 'name', 'repo', 'tags', 'score', 'description', 'chineseDescription', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount', 'downloadCount', 'valid'],
        editProperties: ['id', 'name', 'repo', 'tags', 'score', 'description', 'chineseDescription', 'version', 'authorAvatar', 'banner', 'downloadUrl', 'source', 'isDesktopOnly', 'pluginUpdatedTime', 'valid'],
        filterProperties: ['id', 'name', 'repo', 'tags', 'score', 'description', 'chineseDescription', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount', 'downloadCount', 'valid'],
        sort: {
            direction: 'desc',
            sortBy: 'downloadCount',
        }
    }

}

export const orderResource: ResourceWithOptions = {
    resource: Order,
    options: {
        id: '订单设置',
        listProperties: ['id', 'goodsId', 'goodsCount', 'amount', 'status', 'description', 'createdAt'],
        editProperties: ['status'],
        filterProperties: ['id', 'goodsId', 'status', 'createdAt'],
    }
}

export const thinoResource: ResourceWithOptions = {
    resource: User,
    options: {
        id: 'thino设置',
        listProperties: ['id', 'type', 'thinoWebExpir', 'thino', 'createdAt'],
        editProperties: ['thino', 'thinoWebExpir'],
        filterProperties: ['id', 'type', 'thinoWebExpir', 'thino', 'createdAt'],
    }
}

export const thinoActivateResource: ResourceWithOptions = {
    resource: ActivationRecord,
    options: {
        id: 'thino激活码',
        listProperties: ['creatorId', 'type', 'appid', 'appName', 'createdAt'],
        filterProperties: ['creatorId', 'type', 'appid', 'appName', 'createdAt'],
    }
}

export const checkoutOrderResource: ResourceWithOptions = {
    resource: CheckoutOrder,
    options: {
        id: '2checkout订单',
        listProperties: ['id', 'creatorId', 'goodsName', 'amount', 'currency', 'email', 'country', 'createdAt'],
        filterProperties: ['id', 'creatorId', 'goodsName', 'email', 'country', 'createdAt']
    }
}