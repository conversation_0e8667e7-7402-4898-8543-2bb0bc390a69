import { MigrationInterface, QueryRunner } from "typeorm";

export class userAddSubscribeWechat1691568291806 implements MigrationInterface {
    name = 'userAddSubscribeWechat1691568291806'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`zotero_plugin\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`repo\` varchar(255) NOT NULL, \`author\` varchar(255) NULL, \`authorAvatar\` varchar(255) NULL, \`banner\` varchar(255) NULL, \`tags\` varchar(255) NULL, \`score\` int NULL, \`description\` text NULL, \`version\` varchar(255) NOT NULL, \`pluginUpdatedTime\` datetime NULL, \`updatedTime\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`downloadCount\` int NOT NULL DEFAULT '0', \`pkmerDownloadCount\` int NOT NULL DEFAULT '0', \`downloadUrl\` varchar(255) NOT NULL, \`valid\` tinyint NOT NULL DEFAULT 1, INDEX \`IDX_ef70622302cb7c1e5e34faad11\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`subscribeWechat\` tinyint NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_0fda9260b0aaff9a5b8f38ac16\` ON \`user\` (\`openid\`)`);
        await queryRunner.query(`CREATE INDEX \`IDX_e3f6feab5c4faea9df84b9fc81\` ON \`user\` (\`unionid\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_e3f6feab5c4faea9df84b9fc81\` ON \`user\``);
        await queryRunner.query(`DROP INDEX \`IDX_0fda9260b0aaff9a5b8f38ac16\` ON \`user\``);
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`subscribeWechat\``);
        await queryRunner.query(`DROP INDEX \`IDX_ef70622302cb7c1e5e34faad11\` ON \`zotero_plugin\``);
        await queryRunner.query(`DROP TABLE \`zotero_plugin\``);
    }

}
