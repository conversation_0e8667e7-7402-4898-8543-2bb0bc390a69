import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import monthLeftSeconds from 'src/utils/monthLeftSeconds';

@Injectable()
export class DownloadService {
    oss: any
    constructor(
        private readonly configService: ConfigService,
        private readonly usersService: UsersService,
        @InjectRedis() private readonly redisClient,
    ) {
        const OSS = require('ali-oss');
        this.oss = new OSS({
            region: this.configService.get('oss.ossRegion'),
            accessKeyId: this.configService.get('oss.ossAccessKeyId'),
            accessKeySecret: this.configService.get('oss.ossAccessKeySecret'),
            bucket: 'pkmer-download',
        })
    }

    // 请求插件信息的限制
    async limitApiCallTimes(userID: string): Promise<boolean> {
        //根据userID，限制调用次数为一天100次x
        const redisKey = 'api:' + userID;
        let redisValue = this.redisClient.get(redisKey);
        if (!redisValue) {
            await this.redisClient.set(redisKey, 1, 'EX', 86400);
            redisValue = 1
        }

        if (redisValue && Number(redisValue) > 200) {
            return false
        }
        await this.redisClient.incr(redisKey);
        return true
    }

    async getLeftDownloadCount(userId: string) {
        const redisKey = 'download:' + userId;
        let redisValue = await this.redisClient.get(redisKey);
        if (!redisValue) {
            const leftSeconds = monthLeftSeconds();
            await this.redisClient.set(redisKey, 1, 'EX', leftSeconds);
            redisValue = 1
        }

        const user = await this.usersService.findOne({ id: userId });
        if (user.type === 'insider') {
            return 300 - Number(redisValue);
        } else if ((user.email || user.phone) && user.unionid) {
            return 100 - Number(redisValue);
        }else if ((user.email || user.phone) && !user.unionid) {
            return 5 - Number(redisValue);
        } else if (!(user.email || user.phone) && user.unionid) {
            return 5 - Number(redisValue)
        }else if(!(user.email || user.phone) && !user.unionid){
            return 0
        }
    }

    async limitDownloadTimes(user: User) {
        //redis限制下载次数,每月15次
        const redisKey = 'download:' + user.id;

        const getLeftDownloadCount = await this.getLeftDownloadCount(user.id);
        if (getLeftDownloadCount < 0) {
            return false
        }

        return redisKey
    }

    async uploadImageFileToOss(filename: string, content: any): Promise<string> {
        this.oss.useBucket('pkmer');
        if (!content) {
            throw new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: '请选择文件上传',
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            );
        }

        try {
            await this.oss.head('/covers' + '/' + filename);
            console.log('图片在云端存在，就不更新了: ', filename)
            return 'https://cdn.pkmer.cn/covers/' + filename + '!nomark';
        } catch (e) {
            await this.oss.put('/covers/' + filename, content);
            return 'https://cdn.pkmer.cn/covers/' + filename + '!nomark';
        }
    }

    //上传OSS
    async uploadObsidianPluginZipFile(fileName: string, content: Buffer) {
        this.oss.useBucket('pkmer-download');
        //检查oss是否存在该文件
        try {
            //如果不报错，说明存在
            await this.oss.head('/obsidian-plugins' + '/' + fileName);
            console.log('插件在云端存在，就不更新了: ', fileName)
            //存在，不上传
            return 'https://download.pkmer.cn/obsidian-plugins/' + fileName;
        } catch (e) {
            const result = await this.oss.put('/obsidian-plugins' + '/' + fileName, content);
            return result.url;
        }
    }

    async isObsidianPluginZipFileExist(fileName: string) {
        this.oss.useBucket('pkmer-download');
        try {
            //如果不报错，说明存在
            await this.oss.head('/obsidian-plugins' + '/' + fileName);
            //存在，不上传
            return true;
        } catch (e) {
            console.log('插件在云端不存在', fileName)
            return false;
        }
    }

    // 获取插件的所有版本地址
    async getPluginVersions(pluginId: string): Promise<string[]> {
        try {
            // 切换到对应的 bucket
            this.oss.useBucket('pkmer-download');
            // 设置查询前缀为插件 ID，确保只匹配该插件相关的文件
            const prefix = `obsidian-plugins/${pluginId}-`;
    
            // 调用 OSS 的 list 方法，获取匹配前缀的直接文件
            // 设置 delimiter 为 '/'，确保不列举子文件夹中的文件
            const result = await this.oss.list({
                prefix: prefix,
                delimiter: '/', // 确保只列举当前目录下的文件，不包含子文件夹内容
                'max-keys': 1000, // 最大返回文件数，可根据需求调整
            });
       
            // 从结果中提取文件路径，并转换为下载地址，同时保留 lastModified 时间用于排序
            const versionData = result.objects
                .filter((obj: any) => !obj.name.endsWith('/')) // 过滤掉以 '/' 结尾的文件夹
                .map((obj: any) => ({
                    url: (() => {
                        const fileName = obj.name.split('/').pop(); // 提取文件名
                        return `https://download.pkmer.cn/obsidian-plugins/${fileName}`;
                    })(),
                    lastModified: new Date(obj.lastModified), // 转换为 Date 对象以便排序
                }));
    
            // 按 lastModified 时间降序排序（最新时间靠前）
            versionData.sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime());
    
            // 提取排序后的 URL 列表
            const downloadUrls = versionData.map(item => item.url);
    
            // 可选：日志输出子目录（如果有）
            if (result.prefixes && result.prefixes.length > 0) {
                console.log('Subdirectories found under prefix:', result.prefixes);
            }
    
            return downloadUrls;
        } catch (error) {
            console.error(`获取插件 ${pluginId} 的版本列表失败:`, error);
            throw new Error(`无法获取插件 ${pluginId} 的版本列表: ${error}`);
        }
    }
    

    // 获取插件的所有版本地址
    async getThemeVersions(themeName: string): Promise<string[]> {
        try {
            // 切换到对应的 bucket
            this.oss.useBucket('pkmer-download');
            // 设置查询前缀为插件 ID，确保只匹配该插件相关的文件
            const prefix = `obsidian-themes/${themeName}-`;
    
            // 调用 OSS 的 list 方法，获取匹配前缀的直接文件
            // 设置 delimiter 为 '/'，确保不列举子文件夹中的文件
            const result = await this.oss.list({
                prefix: prefix,
                delimiter: '/', // 确保只列举当前目录下的文件，不包含子文件夹内容
                'max-keys': 1000, // 最大返回文件数，可根据需求调整
            });
       
            // 从结果中提取文件路径，并转换为下载地址，同时保留 lastModified 时间用于排序
            const versionData = result.objects
                .filter((obj: any) => !obj.name.endsWith('/')) // 过滤掉以 '/' 结尾的文件夹
                .map((obj: any) => ({
                    url: (() => {
                        const fileName = obj.name.split('/').pop(); // 提取文件名
                        return `https://download.pkmer.cn/obsidian-themes/${fileName}`;
                    })(),
                    lastModified: new Date(obj.lastModified), // 转换为 Date 对象以便排序
                }));
    
            // 按 lastModified 时间降序排序（最新时间靠前）
            versionData.sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime());
    
            // 提取排序后的 URL 列表
            const downloadUrls = versionData.map(item => item.url);
    
            // 可选：日志输出子目录（如果有）
            if (result.prefixes && result.prefixes.length > 0) {
                console.log('Subdirectories found under prefix:', result.prefixes);
            }
    
            return downloadUrls;
        } catch (error) {
            console.error(`获取主题 ${themeName} 的版本列表失败:`, error);
            throw new Error(`无法获取主题 ${themeName} 的版本列表: ${error}`);
        }
    }
    
    async uploadObsidianThemeZipFile(fileName: string, content: Buffer) {
        this.oss.useBucket('pkmer-download');
        //检查oss是否存在该文件
        try {
            //如果不报错，说明存在
            await this.oss.head('/obsidian-themes' + '/' + fileName);
            console.log('插件在云端存在，就不更新了: ', fileName)
            //存在，不上传
            return 'https://download.pkmer.cn/obsidian-themes/' + fileName;
        } catch (e) {
            console.log('插件在云端不存在，上传', fileName)
            const result = await this.oss.put('/obsidian-themes' + '/' + fileName, content);
            return result.url;
        }
    }

    async isObsidianThemeZipFileExist(fileName: string) {
        this.oss.useBucket('pkmer-download');
        try {
            //如果不报错，说明存在
            await this.oss.head('/obsidian-themes' + '/' + fileName);
            console.log('插件在云端存在: ', fileName)
            //存在，不上传
            return true;
        } catch (e) {
            console.log('插件在云端不存在', fileName)
            return false;
        }
    }

    async uploadZoteroPluginFile(fileName: string, content: Buffer) {
        this.oss.useBucket('pkmer-download');
        //检查oss是否存在该文件
        try {
            //如果不报错，说明存在
            await this.oss.head('/zotero-plugins' + '/' + fileName);
            console.log('插件在云端存在，就不更新了: ', fileName)
        } catch (e) {
            console.log('插件在云端不存在，上传', fileName)
            await this.oss.put('/zotero-plugins' + '/' + fileName, content);
        }
        return 'https://download.pkmer.cn/zotero-plugins/' + fileName;
    }

    async isZoteroPluginZipFileExist(fileName: string) {
        this.oss.useBucket('pkmer-download');
        try {
            //如果不报错，说明存在
            await this.oss.head('/zotero-plugins' + '/' + fileName);
            console.log('插件在云端存在: ', fileName)
            //存在，不上传
            return true;
        } catch (e) {
            console.log('插件在云端不存在', fileName)
            return false;
        }
    }

}
