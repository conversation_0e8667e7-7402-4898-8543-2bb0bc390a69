import { MigrationInterface, QueryRunner } from "typeorm";

export class wechatLogin1683546992831 implements MigrationInterface {
    name = 'wechatLogin1683546992831'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`openid\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`unionid\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`unionid\``);
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`openid\``);
    }

}
