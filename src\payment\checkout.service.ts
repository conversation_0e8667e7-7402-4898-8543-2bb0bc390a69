import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OrdersService } from 'src/orders/orders.service';
import { UsersService } from 'src/users/users.service';
import { handleReplyCheckout, type CheckoutInfo } from './utils/2checkout.util';
import { logger } from 'nestjs-i18n';

@Injectable()
export class CheckoutService {
    constructor(
        private ordersService: OrdersService,
        private usersService: UsersService,
    ) {
    }

    private async validCheckoutInfo(checkoutInfo: CheckoutInfo) {
        if (checkoutInfo.IPN_PNAME[0] !== "Thino") {
            logger.error("错误配置，2checkoutout 未返回 Thino")
            return false
        }
        const userIdIndex = checkoutInfo.IPN_CUSTOM_TEXT.findIndex((val) => val === "UserID")
        if (userIdIndex === -1) {
            logger.error("错误配置，2checkout 未返回 userid")
            return false
        }

        if (checkoutInfo.ORDERSTATUS !== "COMPLETE") {
            return false
        }

        const userid = checkoutInfo.IPN_CUSTOM_VALUE[userIdIndex]

        const user = await this.usersService.findOne({
            id: userid
        })

        if (!user) {
            logger.error("错误配置，2checkout 返回的 userid 未查到用户信息")
            return false
        }

        return user
    }

    async handleThinoPro(checkoutInfo: CheckoutInfo) {
        const user = await this.validCheckoutInfo(checkoutInfo)
        if (!user) {
            return
        }


        try {
            await this.ordersService.createCheckoutOrder(checkoutInfo, user.id)
            await this.usersService.addThinoUser(user.id)
        } catch (e) {
            logger.error("处理2checkout Thino购买失败" + e)
        }

        return handleReplyCheckout(checkoutInfo)
    }
}