import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { Redis } from 'ioredis';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { WeworkThinoMessage } from '../interfaces/wework-thino.interface';
import { ConfigService } from '@nestjs/config';
import { UsersDetailService } from 'src/users/users-detail.service';
import { UsersService } from 'src/users/users.service';
import { UserDetail } from 'src/users/entities/user-detail.entity';
import { ThinoService } from '../service/thino.service';

@Processor('wework-thino')
export class WeworkThinoProcessor {
    private readonly logger = new Logger(WeworkThinoProcessor.name);

    constructor(@InjectRedis() private readonly redisClient: Redis,
        private configService: ConfigService,
        private userDetailService: UsersDetailService,
        private usersService: UsersService,
        private thinoService: ThinoService
    ) {

    }

    async getCorpCustomerServiceAccessToken() {
        let access_token = await this.redisClient.get("corp:customer-access-token")
        if (access_token) {
            return access_token
        }
        const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${this.configService.get("wework.customerServiceId")}&corpsecret=${this.configService.get("wework.customerServiceSecret")}`
        try {
            const response = await fetch(url, {
                method: "GET"
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const { access_token, expires_in } = await response.json();
            if (access_token) {
                await this.redisClient.set("corp:customer-access-token", access_token, 'EX', expires_in)
                return access_token
            } else {
                throw new Error("Get corp access token failed" + access_token)
            }
        } catch (error) {
            console.error('There was a problem with the fetch operation: ', error);
        }
    }

    private async fetchUserId(userExternalId: string): Promise<string> {
        const userDetail = await this.userDetailService.findOne({
            externalId: userExternalId
        })

        if (!userDetail) {
            try {
                const access_token = await this.getCorpCustomerServiceAccessToken()
                const fetchRes = await fetch("https://qyapi.weixin.qq.com/cgi-bin/kf/customer/batchget?access_token=" + access_token, {
                    method: "post",
                    body: JSON.stringify({
                        external_userid_list: [userExternalId]
                    })
                })

                const fetchJson = await fetchRes.json()

                if (!fetchJson || fetchJson.errcode !== 0) {
                    throw new Error("网络错误或者参数错误：" + fetchJson.errmsg)
                }

                const unionid = fetchJson.customer_list[0].unionid

                const userDetail = await this.userDetailService.findOne({
                    unionid
                })

                // 如果user detail 表没有该用户，瞅瞅 user 表有没有
                if (!userDetail) {
                    const user = await this.usersService.findOne({
                        unionid
                    })
                    if (!user) {
                        throw new Error("用户未绑定微信, unionid不存在" + userExternalId)
                    }

                    const existUserDetail = await this.userDetailService.findOne({
                        userId: user.id
                    })

                    if (existUserDetail) {
                        await this.userDetailService.update(existUserDetail.userId, {
                            unionid: user.unionid,
                            externalId: userExternalId
                        })
                        return user.id
                    } else {
                        const newUserDetail = new UserDetail()
                        newUserDetail.userId = user.id
                        newUserDetail.externalId = userExternalId
                        newUserDetail.unionid = unionid
                        await newUserDetail

                        return user.id
                    }

                } else {
                    await this.userDetailService.updateExternalId(userDetail.userId, userExternalId)
                }

                return userDetail.userId


            } catch (error) {
                console.error('通过external id 获取 userid 失败：', error);
            }
        }

        return userDetail.userId
    }

    @Process('createWeworkThino')
    async createWeworkThino(job: Job<WeworkThinoMessage>) {
        const userExternalId = job.data.message.from
        const userId = await this.fetchUserId(userExternalId)
        if (job.data.message.msgtype === "text") {
            await this.thinoService.createThino(userId, {
                type: job.data.message.msgtype,
                content: job.data.message.text.content,
                tags: "#收集/微信"
            })
        } else if (job.data.message.msgtype === "image") {
            await this.thinoService.createThino(userId, {
                type: "wework-image",
                imageSdkid: job.data.message.image.sdkfileid,
                tags: "#收集/微信"
            })
        }

    }
}
