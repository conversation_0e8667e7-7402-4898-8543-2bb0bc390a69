import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    ManyToOne,
    PrimaryGeneratedColumn,
    DeleteDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Allow, IsOptional } from 'class-validator';
import { EntityHelper } from 'src/utils/entity-helper';

@Entity()
export class Forgot extends EntityHelper {
    @PrimaryGeneratedColumn()
    id: number;

    @Allow()
    @Column({ nullable: true })
    @Index()
    @IsOptional()
    email?: string | null;

    @Allow()
    @Column()
    @Index()
    @IsOptional()
    phone?: string | null;

    @Allow()
    @ManyToOne(() => User, {
        eager: true,
    })
    user: User;

    @CreateDateColumn()
    createdAt: Date;

    @DeleteDateColumn()
    deletedAt: Date;
}
