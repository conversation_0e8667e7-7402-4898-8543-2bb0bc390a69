import { Modu<PERSON> } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Order } from './entities/order.entity';
import { OrdersController } from './orders.controller';
import { IsExist } from 'src/utils/validators/is-exists.validator';
import { GoodsModule } from 'src/goods/goods.module';
import { CheckoutOrder } from './entities/checkout-order.entity';
import { UsersModule } from 'src/users/users.module';

@Module({
    imports: [TypeOrmModule.forFeature([Order, CheckoutOrder]), GoodsModule, UsersModule],
    providers: [OrdersService, IsExist],
    controllers: [OrdersController],
    exports: [OrdersService],
})
export class OrdersModule { }
