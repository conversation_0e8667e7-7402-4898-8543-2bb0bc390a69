import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial } from 'src/utils/types/deep-partial.type';
import { FindOptions } from 'src/utils/types/find-options.type';
import { Repository } from 'typeorm';
import { Forgot } from './entities/forgot.entity';

@Injectable()
export class ForgotService {
    constructor(
        @InjectRepository(Forgot)
        private forgotRepository: Repository<Forgot>,
    ) { }

    async findOne(options: FindOptions<Forgot>) {
        return this.forgotRepository.findOne({
            where: options.where,
        });
    }

    async findMany(options: FindOptions<Forgot>) {
        return this.forgotRepository.find({
            where: options.where,
        });
    }

    async create(data: DeepPartial<Forgot>) {
        // console.log(this.forgotRepository.create(data))
        return this.forgotRepository.save(this.forgotRepository.create(data));
    }

    async softDelete(id: number): Promise<void> {
        await this.forgotRepository.softDelete(id);
    }
}
