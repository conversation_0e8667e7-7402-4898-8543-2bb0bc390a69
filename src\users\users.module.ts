import { Modu<PERSON> } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { IsExist } from 'src/utils/validators/is-exists.validator';
import { IsNotExist } from 'src/utils/validators/is-not-exists.validator';
import { UserDetail } from './entities/user-detail.entity';
import { UsersDetailService } from './users-detail.service';
import { Role } from 'src/roles/entities/role.entity';
import { Status } from 'src/statuses/entities/status.entity';

@Module({
    imports: [TypeOrmModule.forFeature([User, UserDetail, Role, Status])],
    controllers: [UsersController],
    providers: [IsExist, IsNotExist, UsersService, UsersDetailService],
    exports: [UsersService, UsersDetailService],
})
export class UsersModule { }
