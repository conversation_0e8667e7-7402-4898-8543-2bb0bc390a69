import { MigrationInterface, QueryRunner } from "typeorm";

export class addThinoOpenidField1702819052730 implements MigrationInterface {
    name = 'addThinoOpenidField1702819052730'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`thinoOpenId\` varchar(255) NULL`);
        await queryRunner.query(`CREATE INDEX \`IDX_d395a1ecedf366892303fc24c8\` ON \`user\` (\`thinoOpenId\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_d395a1ecedf366892303fc24c8\` ON \`user\``);
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`thinoOpenId\``);
    }

}
