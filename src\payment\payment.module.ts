import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { ConfigModule } from '@nestjs/config';
import { PaymentController } from './payment.controller';
import { GoodsModule } from 'src/goods/goods.module';
import { OrdersModule } from 'src/orders/orders.module';
import { UsersModule } from 'src/users/users.module';
import { CheckoutService } from './checkout.service';
import { VerifyModule } from 'src/verify/verify.module';

@Module({
    imports: [ConfigModule, GoodsModule, OrdersModule, UsersModule, VerifyModule],
    providers: [PaymentService, CheckoutService],
    controllers: [PaymentController],
    exports: [PaymentService]
})
export class PaymentModule { }
