import { EntityHelper } from "src/utils/entity-helper";
import { <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { Column, CreateDateColumn, UpdateDateColumn } from "typeorm";
import { PollRecords } from "./poll_records.entity";


@Entity()
export class Poll extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @CreateDateColumn()
    created_ts: Date

    @UpdateDateColumn()
    updated_ts: Date

    @Column()
    poll_name: string

    @Column({nullable: true})
    poll_description: string

    @Column({type: 'longtext'})
    poll_content: string

    @Column({type: 'enum', enum: ['SINGLE', 'MULTIPLE'], default: 'SINGLE'})
    poll_type: string

    @Column({type: 'enum', enum: ['ACTIVE', 'INACTIVE'], default:'INACTIVE'})
    poll_status: string

    @OneToMany(() => PollRecords, poll_records => poll_records.id)
    poll_records: PollRecords[]
}