import { EntityHelper } from 'src/utils/entity-helper';
import { Entity, Column, Index, UpdateDateColumn, PrimaryGeneratedColumn, PrimaryColumn } from 'typeorm';

@Entity()
export class ZoteroPlugin extends EntityHelper {
    @PrimaryColumn()
    id: string;

    @Column()
    @Index()
    name: string;

    @Column()
    repo: string;

    @Column({ nullable: true })
    author: string;

    @Column({ nullable: true })
    authorAvatar: string;

    @Column({ nullable: true })
    banner: string;

    @Column({ nullable: true, default: null })
    tags: string;

    @Column({ default: null })
    score: number;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column()
    version: string;

    @Column({ nullable: true })
    pluginUpdatedTime: Date;

    @UpdateDateColumn()
    updatedTime: Date;

    @Column({ default: 0 })
    downloadCount: number;

    @Column({ default: 0 })
    pkmerDownloadCount: number;

    @Column({ nullable: false })
    downloadUrl: string;

    @Column({ type: 'boolean', default: true })
    valid: boolean;
}