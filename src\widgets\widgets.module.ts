import { Module } from '@nestjs/common';
import { WidgetsService } from './widgets.service';
import { WidgetsController } from './widgets.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Widgets } from './entities/widgets.entity';

@Module({
    imports: [TypeOrmModule.forFeature([Widgets])],
    providers: [WidgetsService],
    controllers: [WidgetsController]
})
export class WidgetsModule { }
