export interface WeworkThinoMessage {
    seq: number
    msgid: string
    message: {
        action: "send"
        from: string
        msgtime: number
        msgtype: "text" | "image"
        image?: {
            sdkfileid: string
        }
        text: {
            content: string
        }
    }
}


export interface ThinoPayload {
    type: "text" | "wework-image"
    imageUrl?: string
    imageSdkid?: string
    content?: string
    tags: string
}