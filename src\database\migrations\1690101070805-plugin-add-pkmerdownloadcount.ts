import { MigrationInterface, QueryRunner } from "typeorm";

export class pluginAddPkmerdownloadcount1690101070805 implements MigrationInterface {
    name = 'pluginAddPkmerdownloadcount1690101070805'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`pkmerDownloadCount\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`pkmerDownloadCount\``);
    }

}
