import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsPhoneNumber, MinLength } from 'class-validator';
import { Transform } from 'class-transformer';

export class AuthWechatBindDto {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsEmail()
    @IsOptional()
    email?: string;

    @ApiProperty({ example: '14998783019' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsPhoneNumber('CN')
    @IsOptional()
    phone?: string

    @ApiProperty({ example: '123456' })
    @MinLength(6)
    @IsOptional()
    password?: string

    @ApiProperty({ example: '134533' })
    @IsOptional()
    captchar?: string

    @ApiProperty({ example: 'o6_bmjrPTlm6_2sgVt7hMZOPfL2M' })
    @IsOptional()
    unionid?: string
}
