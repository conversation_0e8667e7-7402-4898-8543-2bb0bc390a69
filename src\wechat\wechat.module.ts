import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { WechatService } from './service/wechat.service';
import { WechatMpController } from './wechat.controller';
import { XmlMiddleware } from './wechat.middlewar';
import { UsersModule } from 'src/users/users.module';
import { WebsiteModule } from 'src/website/website.module';
import { WechatThinoService } from './service/wechat-thino.service';
import { JwtModule } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { MoonReaderService } from './service/thino-moonreader.service';
import { BullModule } from '@nestjs/bull';
import { WeworkThinoProcessor } from './processor/wework-thino.processor';
import { ThinoService } from './service/thino.service';

@Module({
    imports: [ConfigModule, HttpModule, UsersModule, WebsiteModule, JwtModule.registerAsync({
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
            secret: configService.get('auth.secret'),
            signOptions: {
                expiresIn: configService.get('auth.expires'),
            },
        }),
    }),
        BullModule.registerQueue({
            name: 'wework-thino',
        }),
    ],
    controllers: [WechatMpController],
    providers: [WechatService, WechatThinoService, MoonReaderService, WeworkThinoProcessor, ThinoService],
    exports: [WechatService],
})
export class WechatModule implements NestModule {
    configure(consumer: MiddlewareConsumer) { // 解析 XML 中间件
        consumer.apply(XmlMiddleware).forRoutes('');
    }
}