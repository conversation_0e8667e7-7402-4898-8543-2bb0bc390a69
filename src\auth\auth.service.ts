import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { User } from '../users/entities/user.entity';
import * as bcrypt from 'bcryptjs';
import { AuthLoginDto } from './dto/auth-login.dto';
import { AuthUpdateDto } from './dto/auth-update.dto';
import { RoleEnum } from 'src/roles/roles.enum';
import { StatusEnum } from 'src/statuses/statuses.enum';
import { Status } from 'src/statuses/entities/status.entity';
import { Role } from 'src/roles/entities/role.entity';
import { AuthProvidersEnum } from './auth-providers.enum';
// import { SocialInterface } from 'src/social/interfaces/social.interface';
import { AuthRegisterLoginDto } from './dto/auth-register-login.dto';
import { UsersService } from 'src/users/users.service';
import { ForgotService } from 'src/forgot/forgot.service';
import { MailService } from 'src/mail/mail.service';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis'
import { AuthSendCaptcharDto } from './dto/auth-send-captchar.dto';
import { PkmerResponse } from 'src/utils/types/responese.type';
import { SmsService } from 'src/sms/sms.service';
import { WechatService } from 'src/wechat/service/wechat.service';
import { AuthCheckUserDto } from './dto/auth-check-user.dto';
import { AuthWechatBindDto } from './dto/auth-wechat-bind.dto';
import { AuthBindEmailDto } from './dto/auth-bind-email.dto';

@Injectable()
export class AuthService {
    constructor(
        private jwtService: JwtService,
        private usersService: UsersService,
        private forgotService: ForgotService,
        private mailService: MailService,
        private smsService: SmsService,
        private wechatService: WechatService,
        @InjectRedis() private readonly redis: Redis
    ) { }

    async sendCaptcher(sendCaptcharDto: AuthSendCaptcharDto) {
        const captchar = Math.random().toFixed(6).slice(-6)
        if (sendCaptcharDto.email) {
            let registerCount = await this.redis.get(`count:${sendCaptcharDto.email}`)
            if (!registerCount) {
                await this.redis.set(`count:${sendCaptcharDto.email}`, '0', 'EX', 43200)
                registerCount = '0'
            }

            await this.redis.set(`count:${sendCaptcharDto.email}`, `${Number(registerCount) + 1}`, 'EX', 43200)

            if (Number(registerCount) > 5) {
                return {
                    code: HttpStatus.BAD_REQUEST,
                    message: 'Too many requests',
                }
            }

            await this.redis.set(`${sendCaptcharDto.email}-${captchar}`, captchar, 'EX', 1800)
            await this.mailService.sendCaptchar({
                to: sendCaptcharDto.email,
                data: {
                    captchar,
                },
            });
            return {
                code: 200,
                message: 'Sent successfully',
                payload: {}
            }
        } else if (sendCaptcharDto.phone) {
            let registerCount = await this.redis.get(`count:${sendCaptcharDto.phone}`)
            if (!registerCount) {
                await this.redis.set(`count:${sendCaptcharDto.phone}`, '0', 'EX', 43200)
                registerCount = '0'
            }

            await this.redis.set(`count:${sendCaptcharDto.phone}`, `${Number(registerCount) + 1}`, 'EX', 43200)

            if (Number(registerCount) > 5) {
                return {
                    code: HttpStatus.BAD_REQUEST,
                    message: 'Too many requests',
                }
            }

            await this.redis.set(`${sendCaptcharDto.phone}-${captchar}`, captchar, 'EX', 1800)
            await this.smsService.sendSms(sendCaptcharDto.phone, captchar)
            return {
                code: 200,
                message: 'Sent successfully'
            }
        }
        else {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Email or Phone cannot be empty',
            }
        }
    }

    async validateLogin(
        loginDto: AuthLoginDto,
        onlyAdmin: boolean,
    ): Promise<PkmerResponse> {
        const isPhone = loginDto.phone ? true : false

        let user = undefined
        if (!isPhone) {
            user = await this.usersService.findOne({
                email: loginDto.email,
            });
        } else {
            user = await this.usersService.findOne({
                phone: loginDto.phone,
            });
        }

        if (
            !user ||
            (user &&
                !(onlyAdmin ? [RoleEnum.admin] : [RoleEnum.user]).includes(
                    user.role.id,
                ))
        ) {

            return {
                code: HttpStatus.UNPROCESSABLE_ENTITY,
                message: 'The email is not registered or has no permission',
                payload: {}
            }
        }

        if (user.provider !== AuthProvidersEnum.email && user.provider !== AuthProvidersEnum.phone && user.provider !== AuthProvidersEnum.wechat) {
            return {
                code: HttpStatus.UNPROCESSABLE_ENTITY,
                message: `needLoginViaProvider:${user.provider}`,
                payload: {}
            }
        }

        const isValidPassword = await bcrypt.compare(
            loginDto.password,
            user.password,
        );

        if (isValidPassword) {
            const token = await this.jwtService.sign({
                id: user.id,
                thino: user.thino,
                type: user.type,
            });

            await this.redis.set(`token:${user.id}`, token, 'EX', 2592000)
            return {
                code: HttpStatus.OK,
                message: 'Login successful',
                payload: {
                    token,
                    user
                }
            };
        } else {
            return {
                code: HttpStatus.UNPROCESSABLE_ENTITY,
                message: 'Password Error',
                payload: {}
            }
        }
    }

    async register(dto: AuthRegisterLoginDto): Promise<PkmerResponse> {
        const isPhone = dto.phone ? true : false
        const isAbroad = dto.abroad ? true : false

        //处理用户重复注册
        if (isPhone) {
            const phoneUser = await this.usersService.findOne({
                phone: dto.phone
            })
            if (phoneUser) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Phone number has been registered`,
                    payload: {}
                }
            }
        } else if (!isPhone && !dto.unionid) {
            const emailUser = await this.usersService.findOne({
                email: dto.email
            })
            if (emailUser) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Email has been registered`,
                    payload: {}
                }
            }
        } else if (!isPhone && dto.unionid) {
            const wechatUser = await this.usersService.findOne({
                unionid: dto.unionid
            })
            if (wechatUser) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Wechat has been registered`,
                    payload: {}
                }
            }
        } else {
            return {
                code: HttpStatus.FORBIDDEN,
                message: `Registration information is incomplete`,
                payload: {}
            }
        }


        //处理用户注册逻辑
        if (!isPhone) {
            const emailUser = await this.usersService.findOne({
                email: dto.email
            })
            if (emailUser) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Email has been registered`,
                    payload: {}
                }
            }

            const captchar = await this.redis.get(`${dto.email}-${dto.captchar}`)
            if (captchar != dto.captchar) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Incorrect verification code`,
                    payload: {}
                }
            }
            await this.usersService.create({
                ...dto,
                email: dto.email,
                role: {
                    id: RoleEnum.user,
                } as Role,
                status: {
                    id: StatusEnum.active,
                } as Status,
            });

            return {
                code: HttpStatus.OK,
                message: 'Registration success',
                payload: {}
            }
        } else {
            const captchar = await this.redis.get(`${dto.phone}-${dto.captchar}`)
            if (captchar != dto.captchar) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Incorrect verification code`,
                    payload: {}
                }
            }
            await this.usersService.create({
                ...dto,
                email: null,
                phone: dto.phone,
                provider: 'phone',
                role: {
                    id: RoleEnum.user,
                } as Role,
                status: {
                    id: StatusEnum.active,
                } as Status,
                abroad: isAbroad
            });

            return {
                code: HttpStatus.OK,
                message: 'Registration success',
                payload: {}
            }
        }
    }

    // async confirmEmail(hash: string): Promise<void> {
    //     const user = await this.usersService.findOne({
    //         hash,
    //     });

    //     if (!user) {
    //         throw new HttpException(
    //             {
    //                 status: HttpStatus.NOT_FOUND,
    //                 error: `notFound`,
    //             },
    //             HttpStatus.NOT_FOUND,
    //         );
    //     }

    //     user.hash = null;
    //     user.status = plainToClass(Status, {
    //         id: StatusEnum.active,
    //     });
    //     await user.save();
    // }

    // async forgotPassword(emailOrPhone: string): Promise<void> {
    //     const emailUser = await this.usersService.findOne({
    //         email: emailOrPhone,
    //     });

    //     const phoneUser = await this.usersService.findOne({
    //         phone: emailOrPhone
    //     })

    //     if (emailUser || phoneUser) {
    //         const captchar = Math.random().toFixed(6).slice(-6)
    //         await this.redis.set(`reset-${emailOrPhone}`, captchar)
    //         await this.mailService.forgotPassword({
    //             to: emailOrPhone,
    //             data: {
    //                 captchar,
    //             },
    //         });
    //     } else {
    //         throw new HttpException(
    //             {
    //                 status: HttpStatus.UNPROCESSABLE_ENTITY,
    //                 errors: {
    //                     email: 'emailOrPhoneNotExists',
    //                 },
    //             },
    //             HttpStatus.UNPROCESSABLE_ENTITY,
    //         );
    //     }
    // }

    async resetPassword(password: string, captchar: string, email?: string, phone?: string) {
        const isPhone = phone ? true : false
        let forgot = undefined
        const currentCaptchar = isPhone ? await this.redis.get(`${phone}-${captchar}`) : await this.redis.get(`${email}-${captchar}`)
        if (currentCaptchar != captchar) {
            return {
                code: HttpStatus.FORBIDDEN,
                message: `Incorrect verification code`,
                payload: {}
            }
        }
        if (!isPhone) {
            const user = await this.usersService.findOne({
                email,
            });

            if (!user) {
                return {
                    code: HttpStatus.UNPROCESSABLE_ENTITY,
                    message: 'Email is not registered',
                    payload: {}
                }
            } else {
                await this.forgotService.create({
                    email,
                    user,
                    phone: ''
                });
            }


            forgot = await this.forgotService.findOne({
                where: {
                    email: email,
                }
            })
            if (!forgot) {
                return {
                    code: HttpStatus.UNPROCESSABLE_ENTITY,
                    message: 'Email is not registered',
                    payload: {}
                }
            }


        } else {
            const user = await this.usersService.findOne({
                phone,
            });

            if (!user) {
                return {
                    code: HttpStatus.UNPROCESSABLE_ENTITY,
                    message: 'Phone number is not registered',
                    payload: {}
                }
            } else {
                await this.forgotService.create({
                    phone,
                    user,
                });
            }


            forgot = await this.forgotService.findOne({
                where: {
                    phone,
                }
            })

            if (!forgot) {
                return {
                    code: HttpStatus.UNPROCESSABLE_ENTITY,
                    message: 'Phone number is not registered',
                    payload: {}
                }
            }
        }

        const user = forgot.user;
        user.password = password;
        await user.save();
        await this.forgotService.softDelete(forgot.id);

        return {
            code: 200,
            message: 'Password reset successful'
        }
    }

    async me(user: User): Promise<User> {
        return await this.usersService.getUserInfo({
            id: user.id,
        });
    }

    async update(user: User, userDto: AuthUpdateDto): Promise<User | PkmerResponse> {
        if (userDto.password) {
            if (userDto.oldPassword) {
                const currentUser = await this.usersService.findOne({
                    id: user.id,
                });

                const isValidOldPassword = await bcrypt.compare(
                    userDto.oldPassword,
                    currentUser.password,
                );

                if (!isValidOldPassword) {
                    return {
                        code: HttpStatus.UNPROCESSABLE_ENTITY,
                        message: 'The old password is incorrect',
                        payload: {}
                    }
                }
                currentUser.password = userDto.password
                await currentUser.save()
            } else {
                return {
                    code: HttpStatus.UNPROCESSABLE_ENTITY,
                    message: 'The old password cannot be empty',
                    payload: {}
                }
            }
        }
        delete userDto.password
        delete userDto.oldPassword

        await this.usersService.update(user.id, userDto);

        return this.usersService.findOne({
            id: user.id,
        });
    }

    async softDelete(user: User): Promise<void> {
        await this.usersService.softDelete(user.id);
    }

    async getWechatLoginUrl(isWeChatBrowser: boolean): Promise<PkmerResponse> {
        let url = await this.wechatService.getWechatLoginUrl(isWeChatBrowser);
        return {
            code: HttpStatus.OK,
            message: '',
            payload: {
                url
            }
        };
    }

    async wechatLogin(code: string, isWeChatBrowser: boolean): Promise<PkmerResponse> {
        //获取 unionid
        let accessToken = await this.wechatService.getAccessToken(code, isWeChatBrowser);
        // console.log('token',accessToken)
        if (!accessToken.unionid) {
            return {
                code: HttpStatus.UNPROCESSABLE_ENTITY,
                message: 'Failed to obtain WeChat authorization',
                payload: {}
            }
        }
        let unionid = accessToken.unionid;
        //用 unionid 查询用户
        let user = await this.usersService.findOne({
            unionid: unionid,
        })
        if (user && user.id) {
            //存在，获取token，返回
            const token = await this.jwtService.sign({
                id: user.id,
                thino: user.thino,
                type: user.type,
            });
            await this.redis.set(`token:${user.id}`, token, 'EX', 2592000);
            return {
                code: HttpStatus.OK,
                message: '',
                payload: {
                    token,
                    user
                }
            };
        } else {
            //跳转到注册页面，添加unionid参数
            return {
                code: HttpStatus.OK,
                message: '',
                payload: {
                    unionid
                }
            };
        }
    }

    async bindWechat(dto: AuthWechatBindDto, isLogin: boolean, userId?: string): Promise<PkmerResponse> {
        const isPhone = dto.phone ? true : false

        if (isLogin) {
            const loginUser = await this.usersService.findOne({
                id: userId
            })
            if (dto.email && loginUser.email && loginUser.email != dto.email) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `The bound email address is inconsistent with the actual email address`,
                    payload: {}
                }
            }

            if (dto.phone && loginUser.phone && loginUser.phone != dto.phone) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `The bound phone number is inconsistent with the actual phone number`,
                    payload: {}
                }
            }
        }

        if (!isLogin && isPhone) {
            const captchar = await this.redis.get(`${dto.phone}-${dto.captchar}`)
            if (captchar != dto.captchar) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Incorrect verification code`,
                    payload: {}
                }
            }

        } else if (!isLogin && !isPhone) {
            const captchar = await this.redis.get(`${dto.email}-${dto.captchar}`)
            if (captchar != dto.captchar) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `Incorrect verification code`,
                    payload: {}
                }

            }
        }


        if (!isPhone) {
            const emailUser = await this.usersService.findOne({
                email: dto.email
            });
            const wechatUser = await this.usersService.findOne({
                unionid: dto.unionid
            })

            if (emailUser && wechatUser && emailUser.id === wechatUser.id) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `This WeChat account has been bound to an email address`,
                    payload: {}
                }
            } else if (emailUser && wechatUser && emailUser.id !== wechatUser.id) {
                //合并账号
                if (emailUser.type === 'insider' && wechatUser.type === 'insider') {
                    return {
                        code: HttpStatus.CONFLICT,
                        message: 'Both accounts have been paid, please contact the administrator for processing.',
                        payload: {}
                    }
                }

                let currentUser = emailUser
                currentUser.phone = emailUser.phone || wechatUser.phone
                currentUser.nickName = emailUser.nickName || wechatUser.nickName
                currentUser.email = emailUser.email || wechatUser.email
                currentUser.password = emailUser.password || wechatUser.password || dto.password || null
                currentUser.unionid = emailUser.unionid || wechatUser.unionid
                currentUser.openid = emailUser.openid || wechatUser.openid
                currentUser.rewards = emailUser.rewards + wechatUser.rewards
                currentUser.thino = emailUser.thino || wechatUser.thino
                currentUser.subscribeWechat = wechatUser.subscribeWechat || wechatUser.subscribeWechat
                await currentUser.save()
                await this.usersService.softDelete(wechatUser.id)


                const token = await this.jwtService.sign({
                    id: currentUser.id,
                    type: currentUser.type,
                    thino: currentUser.thino,
                });
                await this.redis.set(`token:${currentUser.id}`, token, 'EX', 2592000);

                return {
                    code: HttpStatus.OK,
                    message: 'merged' + ' ' + currentUser.email + ' ' + currentUser.unionid,
                    payload: {
                        token,
                        user: currentUser
                    }
                }
            }

            if (emailUser && !wechatUser) {
                emailUser.unionid = dto.unionid
                await emailUser.save()
                // await this.usersService.updateUnionid(emailUser.id, dto.unionid);
                const token = await this.jwtService.sign({
                    id: emailUser.id,
                    type: emailUser.type,
                    thino: emailUser.thino,
                });
                await this.redis.set(`token:${emailUser.id}`, token, 'EX', 2592000);
                return {
                    code: HttpStatus.OK,
                    message: 'Successfully bound based on email account',
                    payload: {
                        token,
                        user: emailUser
                    }
                };
            }

            if (!emailUser && wechatUser) {
                wechatUser.email = dto.email
                wechatUser.password = dto.password
                await wechatUser.save()
                // await this.usersService.updateEmail(wechatUser.id, dto.email, dto.password);
                const token = await this.jwtService.sign({
                    id: wechatUser.id,
                    type: wechatUser.type,
                    thino: wechatUser.thino,
                });
                await this.redis.set(`token:${wechatUser.id}`, token, 'EX', 2592000);
                return {
                    code: HttpStatus.OK,
                    message: 'Successfully bound based on WeChat account',
                    payload: {
                        token,
                        user: wechatUser
                    }
                };
            }

            if (!emailUser && !wechatUser) {
                await this.usersService.create({
                    email: dto.email,
                    phone: null,
                    password: dto.password ?? null,
                    unionid: dto.unionid,
                    nickName: dto.email,
                    provider: 'email',
                    role: {
                        id: RoleEnum.user,
                    } as Role,
                    status: {
                        id: StatusEnum.active,
                    } as Status,
                });

                const user = await this.usersService.findOne({
                    email: dto.email,
                })

                const token = await this.jwtService.sign({
                    id: user.id,
                    type: user.type,
                    thino: user.thino,
                });

                await this.redis.set(`token:${user.id}`, token, 'EX', 2592000);

                return {
                    code: HttpStatus.OK,
                    message: 'Registered and bound successfully',
                    payload: {
                        token,
                        user
                    }
                }
            }
        } else {
            const phoneUser = await this.usersService.findOne({
                phone: dto.phone
            })
            const wechatUser = await this.usersService.findOne({
                unionid: dto.unionid
            })

            if (phoneUser && wechatUser && phoneUser.id === wechatUser.id) {
                return {
                    code: HttpStatus.FORBIDDEN,
                    message: `This WeChat account has been bound to a mobile phone number`,
                    payload: {}
                }
            } else if (phoneUser && wechatUser && phoneUser.id !== wechatUser.id) {
                //合并账号
                if (phoneUser.type === 'insider' && wechatUser.type === 'insider') {
                    return {
                        code: HttpStatus.CONFLICT,
                        message: 'Both accounts have been paid, please contact the administrator for processing.',
                        payload: {}
                    }
                }

                let currentUser = phoneUser
                currentUser.nickName = phoneUser.nickName || wechatUser.nickName
                currentUser.phone = phoneUser.phone || wechatUser.phone
                currentUser.email = phoneUser.email || wechatUser.email
                currentUser.password = phoneUser.password || wechatUser.password || dto.password || null
                currentUser.unionid = phoneUser.unionid || wechatUser.unionid
                currentUser.openid = phoneUser.openid || wechatUser.openid
                currentUser.rewards = phoneUser.rewards + wechatUser.rewards
                currentUser.thino = phoneUser.thino || wechatUser.thino
                currentUser.subscribeWechat = wechatUser.subscribeWechat || phoneUser.subscribeWechat
                // await this.usersService.update(currentUser.id, currentUser)
                await currentUser.save()
                await this.usersService.softDelete(wechatUser.id)


                const token = await this.jwtService.sign({
                    id: currentUser.id,
                    type: currentUser.type,
                    thino: currentUser.thino,
                });
                await this.redis.set(`token:${currentUser.id}`, token, 'EX', 2592000);
                return {
                    code: HttpStatus.OK,
                    message: 'Merged' + currentUser.phone + ' ' + ' ' + currentUser.unionid,
                    payload: {
                        token,
                        user: currentUser
                    }
                }
            }

            if (phoneUser && !wechatUser) {
                phoneUser.unionid = dto.unionid
                await phoneUser.save()
                // await this.usersService.updateUnionid(phoneUser.id, dto.unionid);
                const token = await this.jwtService.sign({
                    id: phoneUser.id,
                    type: phoneUser.type,
                    thino: phoneUser.thino,
                });
                await this.redis.set(`token:${phoneUser.id}`, token, 'EX', 2592000);
                return {
                    code: HttpStatus.OK,
                    message: 'Successfully bound based on mobile phone account',
                    payload: {
                        token,
                        user: phoneUser
                    }
                };
            }

            if (!phoneUser && wechatUser) {
                wechatUser.phone = dto.phone
                wechatUser.password = dto.password
                await wechatUser.save()
                // await this.usersService.updatePhone(wechatUser.id, dto.phone, dto.password);
                const token = await this.jwtService.sign({
                    id: wechatUser.id,
                    type: wechatUser.type,
                    thino: wechatUser.thino,
                });
                await this.redis.set(`token:${wechatUser.id}`, token, 'EX', 2592000);
                return {
                    code: HttpStatus.OK,
                    message: 'Successfully bound based on WeChat account',
                    payload: {
                        token,
                        user: wechatUser
                    }
                };
            }

            if (!phoneUser && !wechatUser) {
                await this.usersService.create({
                    email: null,
                    phone: dto.phone,
                    password: dto.password || null,
                    unionid: dto.unionid,
                    nickName: dto.phone,
                    provider: 'phone',
                    role: {
                        id: RoleEnum.user,
                    } as Role,
                    status: {
                        id: StatusEnum.active,
                    } as Status,
                });

                const user = await this.usersService.findOne({
                    phone: dto.phone,
                })

                const token = await this.jwtService.sign({
                    id: user.id,
                    type: user.type,
                    thino: user.thino,
                });

                await this.redis.set(`token:${user.id}`, token, 'EX', 2592000);

                return {
                    code: HttpStatus.OK,
                    message: 'Registered and bound successfully',
                    payload: {
                        token,
                        user
                    }
                }
            }

        }
    }

    async getWechatQrcode(): Promise<PkmerResponse> {
        let url = await this.wechatService.getUserSubscibeUrl();
        return {
            code: HttpStatus.OK,
            message: 'Success',
            payload: {
                url
            }
        };
    }

    async checkUser(dto: AuthCheckUserDto): Promise<PkmerResponse> {
        const isPhone = dto.phone ? true : false;
        const isUuid = dto.uuid ? true : false;
        let user = null;
        if (!isPhone && !isUuid) {
            user = await this.usersService.findOne({
                email: dto.email
            });
        } else if (isPhone && !isUuid) {
            user = await this.usersService.findOne({
                phone: dto.phone
            });
        } else if (isUuid) {
            user = await this.usersService.findOne({
                id: dto.uuid
            });
        }
        return {
            code: HttpStatus.OK,
            message: 'Success',
            payload: {
                userExists: (user && user.id) ? true : false
            }
        };
    }

    async authPkmerForum(payload) {
        // {
        //     client_id: 'pkmer-forum',
        //     client_secret: 'D7MtapNYigoCo8',
        //     code: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjlhZGRhYTNlLWQ0ZGYtNDQ3Mi1hMjYwLWI5YjdmM2I0MGU0YSIsInRoaW5vIjp0cnVlLCJ0eXBlIjoiaW5zaWRlciIsImlhdCI6MTcxMzg0MDc5NSwiZXhwIjoxNzI5MzkyNzk1fQ.9Bw4cY33_yUuJdwhMx9jNGVKGDii6aT26GBcOwfQKJQ',
        //     grant_type: 'authorization_code',
        //     redirect_uri: 'https://forum.pkmer.net/auth/oauth2_basic/callback'
        // }
        if (!payload.client_secret || payload.client_secret !== "D7MtapNYigoCo8") {
            throw new Error("user or email does not exist!")
        }
        const result: any = await this.jwtService.decode(payload.code)
        const userId = result.id
        const user = await this.usersService.findOne({
            id: userId
        })

        if (!user || !user.email) {
            throw new Error("user or email does not exist!")
        }
        return {
            id: result.id,
            access_token: payload.code,
            token_type: "bearer",
            expires_in: 7200,
        }
    }

    async bindEmail(userId: string, payload: AuthBindEmailDto) {
        if (!payload.email || !payload.captchar) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "Email or capthcar does not exist"
            }
        }

        const currentCaptchar = await this.redis.get(`${payload.email}-${payload.captchar}`)
        if (currentCaptchar !== payload.captchar) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "Captchar error"
            }
        }

        const isEmailUserExist = await this.usersService.findOne({
            email: payload.email
        })


        if (isEmailUserExist) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "Email is already bind to another user." + isEmailUserExist.id
            }
        }

        return await this.usersService.update(userId, {
            email: payload.email
        })

    }
}
