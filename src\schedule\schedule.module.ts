import { Module } from '@nestjs/common';
import { ScheduleModule as NestSchedleModule } from '@nestjs/schedule';
import { UpdateSchedule } from './update-schedule';
import { DownloadModule } from 'src/download/download.module';
import { ConfigModule } from '@nestjs/config';
import { OrdersModule } from 'src/orders/orders.module';
import { BackupSchedule } from './backup-database';
// import { RemovePendingOrderSchedule } from './remove-pending-order';

@Module({
    imports: [NestSchedleModule.forRoot(), DownloadModule, ConfigModule, OrdersModule],
    exports: [NestSchedleModule],
    providers: [UpdateSchedule, BackupSchedule],
})
export class ScheduleModule { }
