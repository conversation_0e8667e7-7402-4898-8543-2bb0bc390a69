import { Module } from '@nestjs/common';
import { OssService } from './oss.service';
import { OssController } from './oss.controller';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OSSEntity } from './entities/oss.entity';
import { UsersModule } from 'src/users/users.module';

@Module({
    imports: [TypeOrmModule.forFeature([OSSEntity]), ConfigModule, UsersModule],
    providers: [OssService],
    controllers: [OssController],
    exports: [OssService]
})
export class OssModule { }
