import { InjectRedis } from "@liaoliaots/nestjs-redis";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ZoteroPlugin } from "../entities/zotero_plugin.entity";
import { getZoteroPluginList, getZoteroReleaseByVersion } from "../utils/zoteroPluginUtils";
import type { ZoteroPluginDetail, ZoteroPluginInfo } from "../utils/zoteroPluginUtils";
import { fetchWithRetry } from "../utils/obsidianPluginUtils";
import { DownloadService } from "./download.service";
import { ConfigService } from "@nestjs/config";
import { UsersService } from "src/users/users.service";
import { DownloadRecords } from "../entities/download_records.entity";

@Injectable()
export class ZoteroPluginService {
    constructor(
        private readonly downloadService: DownloadService,
        private readonly configService: ConfigService,
        private readonly usersService: UsersService,
        @InjectRepository(DownloadRecords) private readonly downloadRecordsRepository: Repository<DownloadRecords>,
        @InjectRepository(ZoteroPlugin) private readonly zoteroPluginRepository: Repository<ZoteroPlugin>,
        @InjectRedis() private readonly redisClient,
    ) { }

    private async downloadAndUploadLatestZoteroPlugin(latestZoteroPluginInfo: ZoteroPluginInfo): Promise<ZoteroPluginDetail> {
        const zoteroPluginDetail = await getZoteroReleaseByVersion(latestZoteroPluginInfo, latestZoteroPluginInfo.version)
        if (!zoteroPluginDetail || !zoteroPluginDetail.xpiDownloadUrl) return

        try {
            const zoteroPluginResponse = await fetchWithRetry(zoteroPluginDetail.xpiDownloadUrl)
            const zoteroPluginContent = await zoteroPluginResponse.arrayBuffer()

            if (zoteroPluginContent) {
                const fileName = `${latestZoteroPluginInfo.id}-${latestZoteroPluginInfo.version}.xpi`
                const uploadStatus = await this.downloadService.uploadZoteroPluginFile(fileName, Buffer.from(zoteroPluginContent))
                if (!uploadStatus.startsWith('https://download.pkmer.cn/zotero-plugins/')) {
                    return null
                }

                zoteroPluginDetail.downloadUrl = 'https://download.pkmer.cn/zotero-plugins/' + fileName
                return zoteroPluginDetail
            }
        } catch {
            console.log(`${zoteroPluginDetail.id}-${zoteroPluginDetail.repo} 出问题了`)
        }



        return zoteroPluginDetail
    }

    private async downloadAndUploadAvatarImage(avatarDownloadUrl: string, repo: string) {
        const userName = repo.split('/')[0]

        const avatarImageResponse = await fetchWithRetry(avatarDownloadUrl)
        const avatarImageContent = await avatarImageResponse.arrayBuffer()

        if (avatarImageContent) {
            const fileName = userName + '-avatar.png'
            const avatarUrl = await this.downloadService.uploadImageFileToOss(fileName, Buffer.from(avatarImageContent))
            return avatarUrl
        }
    }

    async updateZoteroPlugins() {
        const zoteroPluginList = await getZoteroPluginList()

        zoteroPluginList.forEach(async (zoteroPluginInfo) => {
            const pluginId = zoteroPluginInfo.id
            const latestVersion = zoteroPluginInfo.version

            const zoteroPluginEntity = await this.zoteroPluginRepository.findOne({
                where: {
                    id: pluginId
                }
            })

            if (zoteroPluginEntity) {
                const currentVersion = zoteroPluginEntity.version
                const isExistInOss = await this.downloadService.isZoteroPluginZipFileExist(`${pluginId}-${latestVersion}.xpi`)
                if (currentVersion === latestVersion && isExistInOss) return
                try {
                    const newZoteroPluginDetail = await this.downloadAndUploadLatestZoteroPlugin(zoteroPluginInfo)
                    if (!newZoteroPluginDetail.downloadUrl) return

                    await this.zoteroPluginRepository.update({ id: pluginId }, {
                        version: newZoteroPluginDetail.version,
                        downloadUrl: newZoteroPluginDetail.downloadUrl,
                        downloadCount: newZoteroPluginDetail.downloadCount,
                        pluginUpdatedTime: newZoteroPluginDetail.updatedAt,
                    })
                } catch (e) {
                    console.log('更新zotero插件失败', zoteroPluginEntity.id, e)
                }

            } else {
                const newZoteroPluginDetail = await this.downloadAndUploadLatestZoteroPlugin(zoteroPluginInfo)
                if (!newZoteroPluginDetail || !newZoteroPluginDetail.downloadUrl) {
                    console.log('未能正确设置zotero插件下载地址', newZoteroPluginDetail)
                    return
                }
                if (!newZoteroPluginDetail || !newZoteroPluginDetail.authorAvatar) {
                    console.log('获取 zotero 插件作者头像失败', newZoteroPluginDetail)
                    return
                }
                const avatarUrl = await this.downloadAndUploadAvatarImage(newZoteroPluginDetail.authorAvatar, newZoteroPluginDetail.repo)


                const zoteroPlugin = new ZoteroPlugin()
                zoteroPlugin.id = newZoteroPluginDetail.id
                zoteroPlugin.name = newZoteroPluginDetail.name
                zoteroPlugin.author = newZoteroPluginDetail.author
                zoteroPlugin.authorAvatar = avatarUrl
                zoteroPlugin.tags = newZoteroPluginDetail.tags.join(',')
                zoteroPlugin.description = newZoteroPluginDetail.description
                zoteroPlugin.version = newZoteroPluginDetail.version
                zoteroPlugin.downloadUrl = newZoteroPluginDetail.downloadUrl
                zoteroPlugin.downloadCount = newZoteroPluginDetail.downloadCount
                zoteroPlugin.pluginUpdatedTime = newZoteroPluginDetail.updatedAt
                zoteroPlugin.repo = newZoteroPluginDetail.repo

                await this.zoteroPluginRepository.insert(zoteroPlugin)
            }
        })
    }

    async generateSignedZoteroPluginDownloadUrl(userId: string, originalUrl: string) {
        const user = await this.usersService.findOne({ id: userId })
        if (!user) {
            throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }

        const isZoteroPluginExist = await this.zoteroPluginRepository.findOne({
            where: {
                downloadUrl: originalUrl
            }
        })

        if (!isZoteroPluginExist) {
            throw new HttpException('抱歉，该插件未找到，请反馈到Pkmer修复！', HttpStatus.NOT_FOUND);
        }

        const redisKey = await this.downloadService.limitDownloadTimes(user)

        if (!redisKey) {
            return {
                code: HttpStatus.FORBIDDEN,
                message: "下载次数超出限制"
            }
        }

        //生成CDN签名URL http://DomainName/Filename?auth_key={<timestamp>-rand-uid-<md5hash>}  有效时长900秒 
        const privateKey = this.configService.get('oss.cdnPrivateKey');

        //签算服务器生成鉴权URL的时间，与鉴权URL有效时长共同控制鉴权URL的失效时间。时间点取自签算服务器的Unix时间戳（Unix时间戳是从UTC时间1970年01月01日00时00分00秒到现在的总秒数，是十进制的整型正数，固定长度为10，与时区无关）。
        const timestamp = Math.round(new Date().getTime() / 1000);
        const uri = originalUrl.replace('https://download.pkmer.cn', '').replaceAll(" ", "%20");

        //随机数，建议使用不含短横线的uuid
        const { v4: uuidv4 } = require('uuid');
        const rand = uuidv4().replace(/-/g, '');
        const uid = '0';

        //通过MD5算法计算出的字符串，由数字0~9和小写英文字母a~z混合组成，固定长度为32。
        const crypto = require('crypto');
        const md5hash = crypto.createHash('md5').update(`${uri}-${timestamp}-${rand}-${uid}-${privateKey}`).digest('hex');

        const auth_key = `${timestamp}-${rand}-${uid}-${md5hash}`;
        const signedUrl = originalUrl + '?auth_key=' + auth_key;

        //redis设置过期时间为一个月，redis放在额外的存储库
        // await this.redisClient.set(redisKey, Number(redisValue) + 1, 'EX', 60 * 60 * 24 * 30);
        await this.redisClient.incr(redisKey);

        await this.zoteroPluginRepository.increment({ downloadUrl: originalUrl }, 'pkmerDownloadCount', 1)
        await this.downloadRecordsRepository.insert({
            creator: user.id,
            downloadType: 'ZoteroPlugin',
            pluginId: isZoteroPluginExist.id,
            version: isZoteroPluginExist.version,
        })

        return signedUrl

    }

    async getTop20Plugins() {
        const zoteroPlugins = await this.zoteroPluginRepository.find({
            order: {
                pkmerDownloadCount: 'DESC'
            },
            take: 20
        })
        return zoteroPlugins
    }

    async getAllPlugins(userId: string) {
        const user = await this.usersService.findOne({ id: userId })
        if (!user) {
            throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }

        if (!await this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        const zoteroPlugins = await this.zoteroPluginRepository.createQueryBuilder('zotero_plugin')
            .orderBy('zotero_plugin.pkmerDownloadCount', 'DESC')
            .where('zotero_plugin.valid = :valid', { valid: true })
            .getMany()

        return zoteroPlugins
    }

    async getPluginsPaginated(userId: string, page: number = 1, limit: number = 10, sortBy: string = 'pkmerDownloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC') {
        const user = await this.usersService.findOne({ id: userId })
        if (!user) {
            throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }

        if (!await this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['pkmerDownloadCount', 'downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'pkmerDownloadCount'; // 默认排序字段
        }

        const [plugins, total] = await this.zoteroPluginRepository
            .createQueryBuilder('zotero_plugin')
            .orderBy(`zotero_plugin.${sortBy}`, sortOrder)
            .where('zotero_plugin.valid = :valid', { valid: true })
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();

        return {
            data: plugins,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder
            }
        };
    }

    async searchPluginsPaginated(userId: string, searchText: string, page: number = 1, limit: number = 10, sortBy: string = 'pkmerDownloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC') {
        const user = await this.usersService.findOne({ id: userId })
        if (!user) {
            throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }

        if (!await this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        searchText = searchText.toLowerCase();

        // 验证排序字段是否有效
        const validSortFields = ['pkmerDownloadCount', 'downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'pkmerDownloadCount'; // 默认排序字段
        }

        const allPlugins = await this.zoteroPluginRepository
            .createQueryBuilder('zotero_plugin')
            .where('zotero_plugin.valid = :valid', { valid: true })
            .getMany();

        let filteredPlugins = searchText.length < 1 ? allPlugins : allPlugins.filter(
            (plugin) =>
                plugin.id.toLowerCase().includes(searchText) || 
                plugin.name.toLowerCase().includes(searchText) || 
                plugin.author.toLowerCase().includes(searchText) || 
                (plugin.description?.toLowerCase().includes(searchText)) || 
                (plugin.tags?.toLowerCase().includes(searchText))
        );

        const total = filteredPlugins.length;
        
        // 根据sortBy和sortOrder对插件进行排序
        filteredPlugins = filteredPlugins.sort((a, b) => {
            if (sortOrder === 'ASC') {
                return a[sortBy] > b[sortBy] ? 1 : -1;
            } else {
                return a[sortBy] < b[sortBy] ? 1 : -1;
            }
        });
        
        // 分页处理
        filteredPlugins = filteredPlugins.slice((page - 1) * limit, page * limit);

        return {
            data: filteredPlugins,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder
            }
        };
    }

    async getPluginTags(userId: string) {
        const user = await this.usersService.findOne({ id: userId })
        if (!user) {
            throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }

        if (!await this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        const allPlugins = await this.zoteroPluginRepository
            .createQueryBuilder('zotero_plugin')
            .where('zotero_plugin.valid = :valid', { valid: true })
            .getMany();

        // 从所有插件中提取标签并计数
        let tagsMap = new Map<string, number>();
        allPlugins.forEach(plugin => {
            if (plugin.tags && typeof plugin.tags === 'string') {
                let tags = plugin.tags.split(',').map(tag => tag.trim());
                tags = tags.filter(tag => tag !== ''); // 过滤掉空字符串
                tags.forEach(tag => {
                    if (tagsMap.has(tag)) {
                        tagsMap.set(tag, tagsMap.get(tag) + 1);
                    } else {
                        tagsMap.set(tag, 1);
                    }
                });
            }
        });

        // 将Map转换为数组，并按照标签数量降序排序
        const tagsArray = Array.from(tagsMap).map(([tag, count]) => ({
            tag,
            count
        })).sort((a, b) => b.count - a.count);

        return tagsArray;
    }

    async getPluginsByTag(userId: string, tag: string, page: number = 1, limit: number = 10, sortBy: string = 'pkmerDownloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC', fuzzyMatch: boolean = false) {
        const user = await this.usersService.findOne({ id: userId })
        if (!user) {
            throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }

        if (!await this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['pkmerDownloadCount', 'downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'pkmerDownloadCount'; // 默认排序字段
        }

        const allPlugins = await this.zoteroPluginRepository
            .createQueryBuilder('zotero_plugin')
            .where('zotero_plugin.valid = :valid', { valid: true })
            .getMany();

        // 筛选包含特定标签的插件，支持模糊匹配
        let filteredPlugins = allPlugins.filter(plugin => {
            if (plugin.tags && typeof plugin.tags === 'string') {
                const tags = plugin.tags.split(',').map(t => t.trim());
                if (fuzzyMatch) {
                    // 模糊匹配：只要标签包含搜索词即可
                    return tags.some(t => t.toLowerCase().includes(tag.toLowerCase()));
                } else {
                    // 精确匹配：标签必须完全匹配
                    return tags.includes(tag);
                }
            }
            return false;
        });

        const total = filteredPlugins.length;
        
        // 根据sortBy和sortOrder对插件进行排序
        filteredPlugins = filteredPlugins.sort((a, b) => {
            if (sortOrder === 'ASC') {
                return a[sortBy] > b[sortBy] ? 1 : -1;
            } else {
                return a[sortBy] < b[sortBy] ? 1 : -1;
            }
        });
        
        // 分页处理
        filteredPlugins = filteredPlugins.slice((page - 1) * limit, page * limit);

        return {
            data: filteredPlugins,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder,
                tag,
                fuzzyMatch
            }
        };
    }
}