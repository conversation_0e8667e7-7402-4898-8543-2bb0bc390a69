import { VerifyProductService } from './verify-product.service';
import { Body, Controller, Get, HttpCode, HttpStatus, Request, Post, Param } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { UseGuards } from '@nestjs/common';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis';

@ApiTags('VerifyProduct')
@Controller({
    path: 'verify-product',
    version: '1',
})
export class VerifyProductController {
    constructor(public verifyProductService: VerifyProductService,
    ) { }

    @ApiOperation({ summary: '发放产品激活码', description: '如未购买，则试用15天，需登录' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('signProductKey/:productId')
    @HttpCode(HttpStatus.OK)
    async signProductKey(@Request() request, @Param("productId") productId: string) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        if (!productId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Need product id',
            };
        }

        return await this.verifyProductService.signProductKey(userId, productId)

    }

    @ApiOperation({ summary: '获取验证信息', description: '传入产品id，需登录' })
    @Get('get-verify-info/:productId')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @HttpCode(HttpStatus.OK)
    async getVerifyInfo(@Request() request, @Param("productId") productId: string) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        if (!productId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Need product id',
            };
        }

        return await this.verifyProductService.getVerifyInfo(userId, productId)
    }


    @ApiOperation({ summary: '验证产品是否有效', description: '传入产品id和userid，无需登录' })
    @Post('verify')
    @HttpCode(HttpStatus.OK)
    async verifyProduct(@Body() verifyOptions: { productId: string; userId: string }) {
        if (!verifyOptions.userId || !verifyOptions.productId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Need product id and user id',
            };
        }

        return await this.verifyProductService.verifyProduct(verifyOptions.userId, verifyOptions.productId)
    }


}
