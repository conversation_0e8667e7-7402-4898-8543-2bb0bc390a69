import { InjectRedis } from "@liaoliaots/nestjs-redis";
import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import { UsersService } from "src/users/users.service";
import { Repository } from "typeorm";
import { DownloadRecords } from "../entities/download_records.entity";
import { ObsidianTheme } from "../entities/obsidian_theme.entity";
import { fetchWithRetry } from "../utils/obsidianPluginUtils";
import { LATEST_OBSIDIAN_THEME_LIST_URL, ThemeInfo, getLatestThemeManifest } from "../utils/obsidianThemeUtils";
import { DownloadService } from "./download.service";
import { InjectQueue } from "@nestjs/bull";
import { Queue } from "bull";


@Injectable()
export class ObsidianThemeService {
    constructor(
        private readonly configService: ConfigService,
        private readonly usersService: UsersService,
        private readonly downloadService: DownloadService,
        @InjectRepository(ObsidianTheme) private readonly obsidianThemeRepository: Repository<ObsidianTheme>,
        @InjectRepository(DownloadRecords) private readonly downloadRecordsRepository: Repository<DownloadRecords>,
        @InjectQueue('obsidian-download') private readonly obsidainPluginDownloadQueue: Queue,
        @InjectRedis() private readonly redisClient,
    ) { }

    public async downloadAndUploadLatestTheme(latestThemeManifest: any, repo: string) {
        const themeCssDownloadUrl = `https://raw.githubusercontent.com/${repo}/master/theme.css`
        const themeCssResponse = await fetchWithRetry(themeCssDownloadUrl)

        const themeCssContent = await themeCssResponse.text()

        if (themeCssContent) {
            const JSZip = require('jszip');
            const zip = new JSZip();
            zip.file('theme.css', themeCssContent);
            zip.file('manifest.json', JSON.stringify(latestThemeManifest))

            const content = await zip.generateAsync({ type: 'blob' });
            const buffer = await content.arrayBuffer();

            const fileName = `${latestThemeManifest.name}-${latestThemeManifest.version}.zip`

            const uploadStatus = await this.downloadService.uploadObsidianThemeZipFile(fileName, Buffer.from(buffer))
            if (!uploadStatus.startsWith('https://download.pkmer.cn/obsidian-themes/')) {
                return null
            }

            return 'https://download.pkmer.cn/obsidian-themes/' + fileName
        }

        return ''
    }

    public async downloadAndUploadBannerImage(screenshot: string, repo: string) {
        const bannerImageDownloadUrl = `https://raw.githubusercontent.com/${repo}/master/${screenshot}`
        const bannerImageResponse = await fetchWithRetry(bannerImageDownloadUrl)

        const bannerImageContent = await bannerImageResponse.arrayBuffer()

        if (bannerImageContent) {
            const fileName = repo.split('/').join('-') + '-' + screenshot
            const bannerUrl = await this.downloadService.uploadImageFileToOss(fileName, Buffer.from(bannerImageContent))
            return bannerUrl
        }

        return ''
    }

    public async downloadAndUploadAvatarImage(repo: string) {
        const userName = repo.split('/')[0]
        const avatarImageGetUrl = `https://api.github.com/users/${userName}`
        try {

            const avatarImageResponse = await fetchWithRetry(avatarImageGetUrl)
            const avatarImageDownloadUrl = (await avatarImageResponse.json()).avatar_url

            const avatarImageResponse2 = await fetchWithRetry(avatarImageDownloadUrl)
            const avatarImageContent = await avatarImageResponse2.arrayBuffer()

            if (avatarImageContent) {
                const fileName = userName + '-avatar.png'
                const avatarUrl = await this.downloadService.uploadImageFileToOss(fileName, Buffer.from(avatarImageContent))
                return avatarUrl
            }
        } catch (e) {
            return ''
        }
    }

    async updateObsidianThemes() {
        const obsidianThemesList = await fetchWithRetry(LATEST_OBSIDIAN_THEME_LIST_URL)
        const obsidianThemeStats = await fetch('https://releases.obsidian.md/stats/theme')

        const obsidianThemeInfoList: ThemeInfo[] = await obsidianThemesList.json()
        const obsidianThemeStatsList = await obsidianThemeStats.json()

        // const removedLegacyThemes = obsidianThemeInfoList.filter((theme) => theme.name === "Blue Topaz")
        const removedLegacyThemes = obsidianThemeInfoList.filter((theme) => !theme.legacy)

        removedLegacyThemes.forEach(async (obsidianThemeInfo) => {
            const { repo, name, screenshot, modes, author } = obsidianThemeInfo
            await this.obsidainPluginDownloadQueue.add("updateObsidianTheme", { repo, name, screenshot, modes, author, downloads: obsidianThemeStatsList[name] ? obsidianThemeStatsList[name].download : 0 }, {
                jobId: repo,
                delay: 500,
                removeOnComplete: true,
                removeOnFail: 10,
                timeout: 180000
            })
        })
    }

    async generateSignedObsidianThemeDownloadUrl(
        userId: string,
        themeName: string,
        version: string
      ) {
        // 判断用户是否存在
        const user = await this.usersService.findOne({ id: userId });
        if (!user) {
          throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }
        // 判断主题是否存在，根据 themeName 和 version 组合查询
        let queryOptions: any = {
          where: {
            name: themeName // 假设主题表有一个 name 字段用于标识主题名称
          }
        };
      
        const isThemeExist = await this.obsidianThemeRepository.findOne(queryOptions);
        if (!isThemeExist) {
          throw new HttpException(
            `抱歉，主题 ${themeName}  版本 ${version}  未找到，请反馈到 Pkmer！`,
            HttpStatus.NOT_FOUND
          );
        }
        // 下载次数限制
        const redisKey = await this.downloadService.limitDownloadTimes(user);
        if (!redisKey) {
          return {
            code: HttpStatus.FORBIDDEN,
            message: "下载次数超出限制"
          };
        }
        // 确定下载链接和版本号
        let downloadUrl: string;
        let finalVersion: string;
        const downloadDomain = 'https://download.pkmer.cn'; // 或者使用 OSS 默认域名 'https://pkmer-download.oss-cn-beijing.aliyuncs.com'
        finalVersion = version;
        const encodedThemeName = encodeURIComponent(themeName);
        downloadUrl = `${downloadDomain}/obsidian-themes/${encodedThemeName}-${version}.zip`;
   
          // 可选：验证指定版本文件是否存在于 OSS（根据需求开启）
          // const filePath = `${themeName}-${version}.zip`;
          // const isThemeInOss = await this.downloadService.isObsidianThemeZipFileExist(filePath);
          // if (!isThemeInOss) {
          //     throw new HttpException(
          //         `抱歉，版本 ${version} 的主题不存在于云端，请检查版本号或反馈到 Pkmer！`,
          //         HttpStatus.NOT_FOUND
          //     );
          // }
      
        // 生成 CDN 签名 URL，http://DomainName/Filename?auth_key={<timestamp>-rand-uid-<md5hash>} 有效时长900秒
        const privateKey = this.configService.get('oss.cdnPrivateKey');
        const timestamp = Math.round(new Date().getTime() / 1000);
        const uri = downloadUrl.replace(downloadDomain, '');
        // 随机数，建议使用不含短横线的 uuid
        const { v4: uuidv4 } = require('uuid');
        const rand = uuidv4().replace(/-/g, '');
        const uid = '0';
        // 通过 MD5 算法计算出的字符串
        const crypto = require('crypto');
        const md5hash = crypto
          .createHash('md5')
          .update(`${uri}-${timestamp}-${rand}-${uid}-${privateKey}`)
          .digest('hex');
        const auth_key = `${timestamp}-${rand}-${uid}-${md5hash}`;
        const signedUrl = downloadUrl + '?auth_key=' + auth_key;
        // Redis 设置下载次数记录
        await this.redisClient.incr(redisKey);
        // 增加主题下载计数
        await this.obsidianThemeRepository.increment({ id: isThemeExist.id }, 'pkmerDownloadCount', 1);
        // 记录下载日志，使用最终确定的版本号
        await this.downloadRecordsRepository.insert({
          creator: userId,
          downloadType: 'ObsidianTheme',
          pluginId: isThemeExist.id, // 假设主题表和插件表共享相同的 ID 字段
          version: finalVersion
        });
        return signedUrl;
      }
      

    async getTop20ObsidianThemes() {
        const top20ObsidianThemes = await this.obsidianThemeRepository
            .createQueryBuilder('obsidian_theme')
            .orderBy('obsidian_theme.pkmerDownloadCount', 'DESC')
            .where('obsidian_theme.valid = :valid', { valid: true })
            .limit(20)
            .getMany()

        return top20ObsidianThemes
    }

    async getAllObsidianThemes(userId: string) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }
        const allObsidianThemes = await this.obsidianThemeRepository
            .createQueryBuilder('obsidian_theme')
            .orderBy('obsidian_theme.updatedTime', 'DESC')
            .where('obsidian_theme.valid = :valid', { valid: true })
            .getMany()

        return allObsidianThemes
    }

    async getThemesPaginated(userId: string, page: number = 1, limit: number = 10, sortBy: string = 'pkmerDownloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC') {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['pkmerDownloadCount', 'id', 'name', 'author', 'version', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'pkmerDownloadCount'; // 默认排序字段
        }

        const [themes, total] = await this.obsidianThemeRepository
            .createQueryBuilder('obsidian_theme')
            .orderBy(`obsidian_theme.${sortBy}`, sortOrder)
            .where('obsidian_theme.valid = :valid', { valid: true })
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();

        return {
            data: themes,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder
            }
        };
    }

    async searchThemesPaginated(userId: string, searchText: string, page: number = 1, limit: number = 10, sortBy: string = 'pkmerDownloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC') {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        searchText = searchText.toLowerCase();

        // 验证排序字段是否有效
        const validSortFields = ['pkmerDownloadCount', 'id', 'name', 'author', 'version', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'pkmerDownloadCount'; // 默认排序字段
        }

        const allThemes = await this.obsidianThemeRepository
            .createQueryBuilder('obsidian_theme')
            .where('obsidian_theme.valid = :valid', { valid: true })
            .getMany();

        let filteredThemes = searchText.length < 1 ? allThemes : allThemes.filter(
            (theme) =>
                theme.id.toLowerCase().includes(searchText) || 
                theme.name.toLowerCase().includes(searchText) || 
                theme.author.toLowerCase().includes(searchText) || 
                (theme.description?.toLowerCase().includes(searchText)) || 
                (theme.chineseDescription?.toLowerCase().includes(searchText))
        );

        const total = filteredThemes.length;
        
        // 根据sortBy和sortOrder对主题进行排序
        filteredThemes = filteredThemes.sort((a, b) => {
            if (sortOrder === 'ASC') {
                return a[sortBy] > b[sortBy] ? 1 : -1;
            } else {
                return a[sortBy] < b[sortBy] ? 1 : -1;
            }
        });
        
        // 分页处理
        filteredThemes = filteredThemes.slice((page - 1) * limit, page * limit);

        return {
            data: filteredThemes,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder
            }
        };
    }

    async getThemeTags(userId: string) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        const allThemes = await this.obsidianThemeRepository
            .createQueryBuilder('obsidian_theme')
            .where('obsidian_theme.valid = :valid', { valid: true })
            .getMany();

        // 从所有主题中提取标签并计数
        let tagsMap = new Map<string, number>();
        allThemes.forEach(theme => {
            if (theme.tags && typeof theme.tags === 'string') {
                let tags = theme.tags.split(',').map(tag => tag.trim());
                tags = tags.filter(tag => tag !== ''); // 过滤掉空字符串
                tags.forEach(tag => {
                    if (tagsMap.has(tag)) {
                        tagsMap.set(tag, tagsMap.get(tag) + 1);
                    } else {
                        tagsMap.set(tag, 1);
                    }
                });
            }
        });

        // 将Map转换为数组，并按照标签数量降序排序
        const tagsArray = Array.from(tagsMap).map(([tag, count]) => ({
            tag,
            count
        })).sort((a, b) => b.count - a.count);

        return tagsArray;
    }

    async getThemesByTag(userId: string, tag: string, page: number = 1, limit: number = 10, sortBy: string = 'pkmerDownloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC', fuzzyMatch: boolean = false) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['pkmerDownloadCount', 'id', 'name', 'author', 'version', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'pkmerDownloadCount'; // 默认排序字段
        }

        const allThemes = await this.obsidianThemeRepository
            .createQueryBuilder('obsidian_theme')
            .where('obsidian_theme.valid = :valid', { valid: true })
            .getMany();

        // 筛选包含特定标签的主题，支持模糊匹配
        let filteredThemes = allThemes.filter(theme => {
            if (theme.tags && typeof theme.tags === 'string') {
                const tags = theme.tags.split(',').map(t => t.trim());
                if (fuzzyMatch) {
                    // 模糊匹配：只要标签包含搜索词即可
                    return tags.some(t => t.toLowerCase().includes(tag.toLowerCase()));
                } else {
                    // 精确匹配：标签必须完全匹配
                    return tags.includes(tag);
                }
            }
            return false;
        });

        const total = filteredThemes.length;
        
        // 根据sortBy和sortOrder对主题进行排序
        filteredThemes = filteredThemes.sort((a, b) => {
            if (sortOrder === 'ASC') {
                return a[sortBy] > b[sortBy] ? 1 : -1;
            } else {
                return a[sortBy] < b[sortBy] ? 1 : -1;
            }
        });
        
        // 分页处理
        filteredThemes = filteredThemes.slice((page - 1) * limit, page * limit);

        return {
            data: filteredThemes,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder,
                tag,
                fuzzyMatch
            }
        };
    }
}