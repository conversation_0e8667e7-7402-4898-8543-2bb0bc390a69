import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PollController } from './poll.controller';
import { PollService } from './poll.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Poll } from './entities/poll.entity';
import { PollRecords } from './entities/poll_records.entity';
import { IsExist } from 'src/utils/validators/is-exists.validator';
import { User } from 'src/users/entities/user.entity';

@Module({
    imports: [TypeOrmModule.forFeature([Poll, PollRecords, User])],
    providers: [PollService, IsExist],
    controllers: [PollController],
    exports: [PollService],
})
export class PollModule { }
