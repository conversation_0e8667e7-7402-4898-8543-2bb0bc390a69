import { MigrationInterface, QueryRunner } from "typeorm";

export class addPluginTagsAvatar1689604167800 implements MigrationInterface {
    name = 'addPluginTagsAvatar1689604167800'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`category\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`authorAvatar\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`tags\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`tags\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`authorAvatar\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`category\` varchar(255) NULL`);
    }

}
