import { MigrationInterface, QueryRunner } from "typeorm";

export class pluginAddDownloadCount1689299724612 implements MigrationInterface {
    name = 'pluginAddDownloadCount1689299724612'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`isDesktopOnly\` tinyint NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`downloadCount\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`downloadCount\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`isDesktopOnly\``);
    }

}
