import { EntityHelper } from "src/utils/entity-helper";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity()
export class Goods extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ type: 'float' })
    price: number;

    @Column()
    description: string;

    //可选 suscribe, widgets, plugins, snippets, articles
    @Column({ default: 'other', type: 'enum', enum: ['suscribe', 'support', 'article', 'crowdfunding', 'other'] })
    type: string;

    @Column({ nullable: true })
    image?: string;


}