//export const PROXY_URL = 'http://74.48.187.113:4320/'
export const PROXY_URL = 'https://gh-proxy.com/'
export const LATEST_OBSIDIAN_PLUGIN_LIST_URL = 'https://raw.githubusercontent.com/obsidianmd/obsidian-releases/master/community-plugins.json';
export const LATEST_OBSIDIAN_PLUGIN_STATS_LIST_URL = 'https://raw.githubusercontent.com/obsidianmd/obsidian-releases/master/community-plugin-stats.json'

//这个是Obsidian官方仓库的插件列表
export interface PluginInfo {
    id: string;
    name: string;
    repo: string;
    author?: string;
    description?: string;
}

export interface ObsidianPluginStats {
    [id: string]: {
        downloads: number
        updated: number
        [version: string]: number
    }
}


//这个是Obsidian插件的manifest.json文件包含的信息
export interface PluginManifest extends PluginInfo {
    version: string;
    authorUrl: string;
    isDesktopOnly: boolean;
    minAppVersion: string;
    previousVersion?: string;
}

//这是获取到的插件的详细信息
export interface PluginDetail {
    manifest: PluginManifest;
    tagName: string;
    repo: string;
    mainJsBrowserDownloadUrl: string;
    stylesCssBrowserDownloadUrl: string;
    downloadUrl?: string;
    downloadCount: number;
    updatedAt: string;
}

// const EXCEPT_PLUGIN_VERSION = {
//     "obsidian-enhancing-mindmap": ["1.2.5"],
//     "obsidian42-brat": ["0.12", "0.11"]
// }

// export function getMaxVersion(versions, pluginId: string) {
//     let maxVersion = null;

//     for (let version in versions) {
//         if (isValidVersion(version) && !EXCEPT_PLUGIN_VERSION[pluginId]?.includes(version)) {
//             if (
//                 maxVersion === null ||
//                 compareVersions(version, maxVersion) > 0
//             ) {
//                 maxVersion = version;
//             }
//         }
//     }

//     return maxVersion;
// }

//fetch失败重复三次，继续失败记录日志
export async function fetchWithRetry(url: string, options?: any, retryCount = 3): Promise<Response> {
    const proxyedUrl = url.replace('https://', PROXY_URL);
    // 此处等待300ms，防止请求太快被github拒绝
    try {
        return fetch(proxyedUrl, {
            timeout: 10000, headers: {
                'Authorization': 'token ****************************************'
            }, ...options
        });
    } catch (error) {
        if (retryCount > 0) {
            console.log(`fetch ${url} failed, retrying...`);

            return fetchWithRetry(url, { timeout: 10000, ...options }, retryCount - 1);
        } else {
            console.log(`fetch ${url} failed, no retry`);
        }
    }
}

export const getPluginLatestReleaseInfo = async (repo: string) => {

    let response = await fetch(`https://amazing.pkmer.cn/proxy/api.github.com/repos/${repo}/releases/latest`);

    const { tag_name, assets } = await response.json();
    if (!tag_name || !assets) {
        throw new Error("获取最新的插件版本错误，无版本名和附件" + `https://api.github.com/repos/${repo}/releases/latest`)
    }
    const manifestJson = assets.find((asset: any) => asset.name === 'manifest.json');
    const manifestJsonResponse = await fetchWithRetry(manifestJson.browser_download_url);
    const mainJs = assets.find((asset: any) => asset.name === 'main.js');
    const stylesCss = assets.find((asset: any) => asset.name === 'styles.css');

    const manifest = await manifestJsonResponse.json() as PluginManifest;

    if (!manifest.author) {
        manifest.author = repo.split('/')[0];
    }

    return {
        manifest,
        repo: repo,
        downloadCount: mainJs.download_count,
        tagName: tag_name,
        updatedAt: mainJs.updated_at,
        mainJsBrowserDownloadUrl: mainJs.browser_download_url,
        stylesCssBrowserDownloadUrl: stylesCss ? stylesCss.browser_download_url : '',
    };
}


export const getReleaseByVersion = async (repo: string, version: string) => {
    let response = await fetchWithRetry(`https://api.github.com/repos/${repo}/releases/tags/${version}`);

    const { tag_name, assets } = await response.json();

    if (tag_name !== version) {
        console.log('版本号不对', repo)
        return null
    }

    if (!assets) {
        console.log('plugin fetch error', repo)
        return null
    }
    const manifestJson = assets.find((asset: any) => asset.name === 'manifest.json');
    const manifestJsonResponse = await fetchWithRetry(manifestJson.browser_download_url);
    const mainJs = assets.find((asset: any) => asset.name === 'main.js');
    const stylesCss = assets.find((asset: any) => asset.name === 'styles.css');

    const manifest = await manifestJsonResponse.json() as PluginManifest;

    if (!manifest.author) {
        manifest.author = repo.split('/')[0];
    }

    if (manifest.version !== version) {
        manifest.version = version;
        manifest.previousVersion = manifest.version;
    }

    return {
        manifest,
        repo: repo,
        version: tag_name,
        downloadCount: mainJs.download_count,
        updatedAt: mainJs.updated_at,
        mainJsBrowserDownloadUrl: mainJs.browser_download_url,
        stylesCssBrowserDownloadUrl: stylesCss ? stylesCss.browser_download_url : '',
    };
}


// obsidian-stat.json 解析成最新版本号、下载量、更新时间
// export function getLatestVersionInfo(obj: { [key: string]: any }, pluginId: string) {

//     const maxVersion = getMaxVersion(obj, pluginId);

//     const latestUpdated = obj.updated;
//     const latestDownloads = obj.downloads;

//     return { version: maxVersion, downloads: latestDownloads, updated: latestUpdated };
// }

// function isValidVersion(version) {
//     // 检查版本号是否符合规则
//     return /^(\d+\.)?(\d+\.)?(\d+)(-[a-zA-Z0-9]+)?$/.test(version);
// }

// function compareVersions(version1, version2) {
//     const parts1 = version1.split(".").map((part) => parseInt(part));
//     const parts2 = version2.split(".").map((part) => parseInt(part));

//     for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
//         const part1 = i < parts1.length ? parts1[i] : 0;
//         const part2 = i < parts2.length ? parts2[i] : 0;

//         if (part1 > part2) {
//             return 1;
//         } else if (part1 < part2) {
//             return -1;
//         }
//     }

//     return 0;
// }