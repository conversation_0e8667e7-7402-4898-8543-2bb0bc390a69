import { MigrationInterface, QueryRunner } from "typeorm";

export class createArticleVisited1700746723878 implements MigrationInterface {
    name = 'createArticleVisited1700746723878'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`article\` ADD \`visited\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`article\` DROP COLUMN \`visited\``);
    }

}
