import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { ThinoPayload, WeworkThinoMessage } from '../interfaces/wework-thino.interface';
import { JwtService } from '@nestjs/jwt';

// 微信订阅号
@Injectable()
export class ThinoService {
    constructor(
        private configService: ConfigService,
        private jwtService: JwtService,
        @InjectRedis() private readonly redisClient: Redis,
        @InjectQueue('wework-thino') private readonly weworkThinoQueue: Queue
    ) { }

    async createThino(userId: string, thinoPayload: ThinoPayload) {
        const THINO_ENDPOINT = "https://api.thino.pkmer.cn/test/"
        const createThinoUrl = THINO_ENDPOINT + 'thino/createThino';

        let token = await this.redisClient.get(`token:${userId}`);

        if (!token) {
            token = await this.jwtService.sign({
                id: userId,
            });
            await this.redisClient.set(`token:${userId}`, token, 'EX', 2592000);
        }

        if (thinoPayload.type === "text") {

            try {
                const thinoRes = await fetch(createThinoUrl, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": "Bearer " + token,
                    },
                    body: JSON.stringify({
                        content: thinoPayload.content + " #收集/微信"
                    })
                });

                const thinoData = await thinoRes.json();
                if (thinoData.code !== 200) {

                    throw new Error("Create Thino Error: thino serverless 创建thino 返回值不为200")
                }

                return true
            } catch (e) {
                throw new Error("Create Thino Error" + e)
            }
        } else if (thinoPayload.type === "wework-image") {
            if (!thinoPayload.imageSdkid) {
                throw new Error("image sdkid not exist!")
            }

            const imageResponse = await fetch("http://127.0.0.1:9000/get_media_data", {
                method: "post",
                body: JSON.stringify({
                    sdk_file_id: thinoPayload.imageSdkid,
                    timeout: 10
                })
            })

            const imageResult = await imageResponse.json()
            if (!imageResult.data || imageResult.err_code !== 0) {
                throw new Error("Get sdkid image error!")
            }

            //data:image/jpeg;base64,
            const imageBuffer = Buffer.from(imageResult.data, "base64")
            const imageBlob = new Blob([imageBuffer], { type: "image/png" })
            const uploadThinoAssetUrl = THINO_ENDPOINT + 'asset/uploadAsset';

            try {
                const form = new FormData()
                form.append("file", imageBlob, "image.png")
                const uploadThinoAssetRes = await fetch(uploadThinoAssetUrl, {
                    method: "POST",
                    headers: {
                        "Authorization": "Bearer " + token,
                    },
                    body: form
                });

                const uploadThinoAssetData = await uploadThinoAssetRes.json();
                if (uploadThinoAssetData.code !== 200) {
                    throw new Error("wework上传图片失败")
                }
                if (uploadThinoAssetData.payload.url) {
                    const thinoRes = await fetch(createThinoUrl, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            "Authorization": "Bearer " + token,
                        },
                        body: JSON.stringify({
                            content: `![](${uploadThinoAssetData.payload.url})` + " #收集/微信",
                        })
                    });

                    const thinoData = await thinoRes.json();
                    if (thinoData.code !== 200) {
                        throw new Error("wework 创建图片 Thino 失败")
                    }
                }
            } catch (e) {
                throw new Error("wework 创建图片 Thino 失败" + e)
            }


        } else {
            console.log("处理会话存储失败, 未定义类型", thinoPayload)
        }
    }


    async getCorpAccessToken() {
        let access_token = await this.redisClient.get("corp:access-token")
        if (access_token) {
            return access_token
        }
        const url = `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=${this.configService.get("wework.corpId")}&corpsecret=${this.configService.get("wework.corpSecret")}`
        try {
            const response = await fetch(url, {
                method: "GET"
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const { access_token, expires_in } = await response.json();
            if (access_token) {
                await this.redisClient.set("corp:access-token", access_token, 'EX', expires_in)
                return access_token
            } else {
                throw new Error("Get corp access token failed" + access_token)
            }
        } catch (error) {
            console.error('There was a problem with the fetch operation: ', error);
        }

    }

    async handleThinoHelperMessage() {
        // {
        //   message: '<xml><ToUserName><![CDATA[wwc033d3c142438021]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1708442045</CreateTime><MsgType><![CDATA[event]]></MsgType><AgentID>2000004</AgentID><Event><![CDATA[msgaudit_notify]]></Event></xml>',
        //   id: 'wwc033d3c142438021',
        //   random: <Buffer 36 35 30 37 35 33 35 30 39 35 64 35 31 37 61 32>
        // }
        // const xmlMessage = decrypt(encodingAES, encryptMessage)
        // const parser = new xmlParser.XMLParser();
        // let message = parser.parse(xmlMessage.message)['xml'];
        //console.log(message)
        // {
        //     ToUserName: 'wwc033d3c142438021',
        //     FromUserName: 'sys',
        //     CreateTime: 1708508957,
        //     MsgType: 'event',
        //     AgentID: 2000004,
        //     Event: 'msgaudit_notify'
        // }
        // if (message.CreateTime === messageCreateTime) {
        //     return
        // } else {
        //     messageCreateTime = message.CreateTime
        // }

        const seq = await this.redisClient.get("corp:seq") || 0
        try {
            // const { data } = await this.httpService.axiosRef.post("http://localhost:9000/get_chat_data", {
            //     "seq": seq,
            //     "limit": 100,
            //     "timeout": 3
            // })

            // console.log(data)
            const response = await fetch("http://127.0.0.1:9000/get_chat_data", {
                method: "POST",
                body: JSON.stringify(
                    {
                        "seq": seq,
                        "limit": 100,
                        "timeout": 30
                    }
                )
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const chatDataRes = await response.json();
            const chatData: WeworkThinoMessage[] = chatDataRes.data

            // console.log(chatData)

            chatData.forEach(async (data: WeworkThinoMessage) => {
                await this.weworkThinoQueue.add("createWeworkThino", data, {
                    jobId: data.msgid,
                    delay: 200,
                    removeOnComplete: 500,
                    removeOnFail: 500
                })
            })

            await this.redisClient.set("corp:seq", chatData[chatData.length - 1].seq, "EX", 60)
            await this.redisClient.persist("corp:seq")


        } catch (error) {
            console.error('There was a problem with the fetch operation: ', error);
        }

        return ""
    }

}


