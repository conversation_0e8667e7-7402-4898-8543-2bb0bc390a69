import { Injectable } from '@nestjs/common';
import WeChatMsgQueue from '../utils/wechatMsgQueue';
import { JwtService } from '@nestjs/jwt';

interface MoonReaderMessage {
    text: string;
    title?: string;
    author?: string;
    chapter?: string;
    image_url?: string;
    source_url?: string;
    source_type?: string;
    category?: string;
    note?: string;
    location?: string;
    location_type?: string;
    highlight_at?: string;
    highlight_url?: string;
}

// 静读天下
const THINO_ENDPOINT = "https://api.thino.pkmer.cn/test/"

// {
//   highlights: [
//     {
//       title: 'JavaScript忍者秘籍（第2版）（异步图书）',
//       text: '回调函数',
//       author: 'JohnResig\nBearBibeault\nJosipMaras',
//       chapter: '3.1.2 回调函数',
//       note: 'fwe'
//     }
//   ]
// }
@Injectable()
export class MoonReaderService {
    public wechatMessageQueue = new WeChatMsgQueue(9999);

    constructor(
        private jwtService: JwtService,
    ) { }

    async addHighlight(token: string, message: MoonReaderMessage[]) {
        const userId = this.jwtService.verify(token).id
        if (!userId) {
            return {
                code: 400,
                message: '用户不存在'
            }
        }

        if (!message) {
            return {
                code: 400,
                message: '参数错误, message 不存在'
            }
        }

        let thinoContent = ""

        if (message.length === 1) {
            const thinoTitle = message[0].title ? message[0].title : undefined
            const thinoText = message[0].text ? message[0].text.trimStart().trimEnd() : undefined
            const thinoChapter = message[0].chapter ? message[0].chapter.trimStart().trimEnd() : undefined
            const thinoAuthor = message[0].author ? message[0].author : undefined
            const thinoNote = message[0].note ? message[0].note.trimStart().trimEnd() : undefined
            if (thinoTitle && thinoText && thinoChapter && thinoAuthor && thinoNote) {
                thinoContent = "> " + thinoText + "——《" + thinoTitle + "》" + thinoChapter + "\n\n" + thinoNote + " #收集/静读天下"
            } else if (thinoTitle && thinoText && thinoChapter) {
                thinoContent = "> " + thinoText + "——《" + thinoTitle + "》" + thinoChapter + "\n\n" + "#收集/静读天下"
            } else if (thinoTitle && thinoText) {
                thinoContent = "> " + thinoText + "——《" + thinoTitle + "》" + "\n\n" + "#收集/静读天下"
            } else if (thinoText) {
                thinoContent = "> " + thinoText + "\n\n" + "#收集/静读天下"
            } else {
                return {
                    code: 400,
                    message: '不受支持的参数类型，请联系开发者开发。'
                }
            }
        }

        if (message.length > 1) {
            return {
                code: 400,
                message: '参数错误, 不能同时添加多个 highlight'
            }
        }

        if (thinoContent.length > 1000) {
            return {
                code: 400,
                message: '参数错误, 文本长度不能超过 500'
            }
        }

        if (!thinoContent) {
            return {
                code: 400,
                message: '参数错误, 文本不能为空'
            }
        }

        const createThinoUrl = THINO_ENDPOINT + 'thino/createThino';
        const thinoRes = await fetch(createThinoUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + token,
            },
            body: JSON.stringify({
                content: thinoContent
            })
        });

        const thinoData = await thinoRes.json();
        if (thinoData.code !== 200) {
            return {
                code: 400,
                message: '添加失败'
            }
        }

        return {
            code: 200,
            message: '添加成功'
        }

    }
}