import {
    Controller,
    Get,
    Param,
    Post,
    Response,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiProperty, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { FilesService } from './files.service';

@ApiTags('Files')
@Controller({
    path: 'files',
    version: '1',
})
export class FilesController {
    constructor(private readonly filesService: FilesService) {}

    @ApiOperation({summary: '上传文件', description: '无限制'})
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('upload')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    async uploadFile(@UploadedFile() file) {
        return this.filesService.uploadFile(file);
    }

    // @ApiOperation({summary: '下载文件', description: '废弃'})
    // @Get(':path')
    // download(@Param('path') path, @Response() response) {
    //     return response.sendFile(path, { root: './files' });
    // }
}
