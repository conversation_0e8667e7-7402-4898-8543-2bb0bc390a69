import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, Min<PERSON>ength, Validate } from 'class-validator';
import { IsExist } from '../../utils/validators/is-exists.validator';
import { FileEntity } from '../../files/entities/file.entity';

export class AuthUpdateDto {
    @ApiProperty({ type: () => FileEntity })
    @IsOptional()
    @Validate(IsExist, ['FileEntity', 'id'], {
        message: 'imageNotExists',
    })
    photo?: FileEntity;

    @ApiProperty({ example: 'John' })
    @IsOptional()
    @IsNotEmpty({ message: 'mustBeNotEmpty' })
    nickName?: string;

    @ApiProperty()
    @IsOptional()
    @IsNotEmpty()
    @MinLength(6)
    password?: string;

    @ApiProperty()
    @IsOptional()
    @IsNotEmpty({ message: 'mustBeNotEmpty' })
    oldPassword?: string;

    @ApiProperty()
    @IsOptional()
    @IsNotEmpty({ message: 'mustBeNotEmpty' })
    article?: string;
}
