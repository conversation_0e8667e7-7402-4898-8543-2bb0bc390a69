import { fetchWithRetry } from "./obsidianPluginUtils";

const PROXY_URL = 'https://amazing.pkmer.cn/proxy/'

export const LATEST_OBSIDIAN_THEME_LIST_URL = 'https://raw.githubusercontent.com/obsidianmd/obsidian-releases/master/community-css-themes.json';

export interface ThemeInfo {
    name: string;
    author: string;
    repo: string;
    screenshot: string;
    modes: string[];
    legacy?: boolean;
}

export interface ThemeManifest extends ThemeInfo {
    version: string;
    authorUrl: string;
}


export const getLatestThemeManifest = async (repo: string) => {
    const url = `https://raw.githubusercontent.com/${repo}/master/manifest.json`;
    const response = await fetchWithRetry(url);
    let manifest
    try {
        manifest = await response.json() as ThemeManifest;
    }catch(e){
        console.log('解析主题信息失败', repo, e)
        return null
    }
    return manifest;
}