import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';


@Injectable()
export class BackupSchedule implements OnModuleInit {
    oss: any
    constructor(
        private readonly schedulerRegistry: SchedulerRegistry,
        private readonly configService: ConfigService,
    ) {
        const OSS = require('ali-oss');
        this.oss = new OSS({
            region: this.configService.get('oss.ossRegion'),
            accessKeyId: this.configService.get('oss.ossAccessKeyId'),
            accessKeySecret: this.configService.get('oss.ossAccessKeySecret'),
            bucket: 'pkmer-backup',
        })

    }

    onModuleInit() {
        this.schedulerRegistry.addCronJob('backupDatabase', this.backupDatabase.bind(this));
    }

    @Cron(CronExpression.EVERY_DAY_AT_4AM)
    // @Cron(CronExpression.EVERY_5_MINUTES)
    async backupDatabase() {
        Logger.log('备份数据库', 'BackupDatabase');
        try {
            const child_process = require('child_process');
            const dumpFileName = `backup/${Date.now().toString()}.dump.sql`;
            child_process.exec(`docker exec pkmer-mysql sh -c 'exec mysqldump pkmer-mysql -uroot -p${this.configService.get('database.password')}' > ${dumpFileName}`, (error, stdout, stderr) => {
                if (error) {
                    Logger.error(error);
                    return;
                }
                this.oss.put("/database/" + dumpFileName.split("/")[1], dumpFileName, {timeout: 600000}).then((res) => {
                    child_process.exec(`rm -rf ${dumpFileName}`, (error, stdout, stderr) => {
                        if (error) {
                            Logger.error(error);
                            return;
                        }
                        Logger.log("备份至OSS并删除本地文件成功！！！")
                    })
                })
            })


        } catch (e) {
            Logger.error(e)
        }
    }

}
