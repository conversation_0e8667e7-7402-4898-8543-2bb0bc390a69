import { Module } from '@nestjs/common';
import { GoodsService } from './goods.service';
import { Goods } from './entities/goods.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GoodsController } from './goods.controller';

@Module({
    imports: [TypeOrmModule.forFeature([Goods])],
    providers: [GoodsService],
    controllers: [GoodsController],
    exports: [GoodsService]
})
export class GoodsModule { }
