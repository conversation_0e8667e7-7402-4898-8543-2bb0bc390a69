import { EntityHelper } from "src/utils/entity-helper";
import { Entity, Index } from "typeorm";
import { Column, CreateDateColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity()
export class PollRecords extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @CreateDateColumn()
    created_ts: Date

    @UpdateDateColumn()
    updated_ts: Date

    @Index()
    @Column({ nullable: true, type: 'uuid' })
    creator: string

    @Column({ nullable: true })
    poll_id: string

    //投票结果
    @Column()
    poll_result: string
}