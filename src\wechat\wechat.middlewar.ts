import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response } from 'express';

import * as bodyParser from 'body-parser';


const bodyParserMiddleware = bodyParser.text({
  limit: '1024kb',
  type: 'text/xml',
});


@Injectable()
export class XmlMiddleware implements NestMiddleware {
  async use(req: Request, res: Response,  next: () => void) {
    bodyParserMiddleware(req, res, next);
  }
}
