import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Goods } from './entities/goods.entity';
import { Repository } from 'typeorm';

@Injectable()
export class GoodsService {
    constructor(
        @InjectRepository(Goods)
        private goodsRepository: Repository<Goods>
    ) { }

    async findOne(goodsId: string) {
        return this.goodsRepository.findOne({ where: { id: goodsId }});
    }

    async findAll(){
        return this.goodsRepository.find()
    }
}
