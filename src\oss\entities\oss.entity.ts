import {
    Column,
    Entity,
    PrimaryGeneratedColumn,
    AfterLoad,
    AfterInsert,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsOptional } from 'class-validator';
import { EntityHelper } from 'src/utils/entity-helper';
import appConfig from '../../config/app.config';

@Entity({ name: 'oss' })
export class OSSEntity extends EntityHelper {
    @ApiProperty({ example: 'cbcfa8b8-3a25-4adb-a9c6-e325f0d0f3ae' })
    @PrimaryGeneratedColumn('uuid')
    id: string;


    @ApiProperty({ example: 'cbcfa8b8-3a25-4adb-a9c6-e325f0d0f3ae' })
    @Column()
    creator: string;

    @ApiProperty({ example: 'test' })
    @Column()
    @IsOptional()
    name: string

    @Allow()
    @Column()
    path: string;

    @AfterLoad()
    @AfterInsert()
    updatePath() {
        if (this.path.indexOf('/') === 0) {
            this.path = appConfig().backendDomain + this.path;
        }
    }
}
