import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    UpdateDateColumn,
    PrimaryColumn,
} from 'typeorm';
import { EntityHelper } from 'src/utils/entity-helper';

@Entity()
export class UserDetail extends EntityHelper {
    @PrimaryColumn({nullable: false})
    userId: string;

    @Column({ nullable: true })
    unionid?: string | null;

    @Column({ nullable: true })
    externalId?: string | null;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn()
    deletedAt: Date | null;
}
