import { MigrationInterface, QueryRunner } from "typeorm";

export class createArticle<PERSON>ield1696430981042 implements MigrationInterface {
    name = 'createArticleField1696430981042'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`article\` json NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`article\``);
    }

}
