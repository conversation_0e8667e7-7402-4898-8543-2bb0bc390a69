import { PartialType } from "@nestjs/swagger";
import { CreateOrderDto } from "./create-order.dto";
import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, Validate } from "class-validator";
import { IsExist } from "src/utils/validators/is-exists.validator";


export class UpdateOrderDto extends PartialType(CreateOrderDto) {
    @ApiProperty({ example: 'description' })
    @IsOptional()
    description?: string;

    // @ApiProperty({ example: 123.45 })
    // @IsOptional()
    // price?: number;

    @ApiProperty({ example: '1511d94f-278d-4cc9-b68a-b1497c8fcbb7' })
    @Validate(IsExist, ['Goods', 'id'], { message: 'goodsNotExists' })
    @IsOptional()
    goodsId?: string;

    @ApiProperty({ example: 1 })
    goodsCount?: number;

    // @ApiProperty({ example: 'pending' })
    // @IsOptional()
    // 'pending' | 'paid' | 'refunded' | 'canceled'
    // status?: string
}