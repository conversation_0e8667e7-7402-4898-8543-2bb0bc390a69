import { MigrationInterface, QueryRunner } from "typeorm";

export class addThinoField1699692893030 implements MigrationInterface {
    name = 'addThinoField1699692893030'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`thino\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`thino\``);
    }

}
