import { MigrationInterface, QueryRunner } from "typeorm";

export class createActivationCodeRow1709881246745 implements MigrationInterface {
    name = 'createActivationCodeRow1709881246745'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`activation_record\` ADD \`code\` longtext NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`activation_record\` DROP COLUMN \`code\``);
    }

}
