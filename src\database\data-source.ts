import path from 'path';
import 'reflect-metadata';
import { DataSource, DataSourceOptions } from 'typeorm';

export const AppDataSource = new DataSource({
    type: process.env.DATABASE_TYPE,
    url: process.env.DATABASE_URL,
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    synchronize: process.env.DATABASE_SYNCHRONIZE === 'true',
    dropSchema: false,
    keepConnectionAlive: true,
    logging: process.env.NODE_ENV !== 'prod',
    entities: [__dirname + '/../**/*.entity{.ts,.js}'],
    autoLoadEntities: true,
    migrations: [__dirname + '/migrations/**/*{.ts,.js}'],
    cli: {
        entitiesDir: 'src',
        migrationsDir: 'src/database/migrations',
        subscribersDir: 'subscriber',
    },
    extra: {
        // based on https://node-postgres.com/api/pool
        // max connection pool size
        max: parseInt(process.env.DATABASE_MAX_CONNECTIONS, 10) || 100,
        ssl:
            process.env.DATABASE_SSL_ENABLED === 'true'
                ? {
                    rejectUnauthorized:
                        process.env.DATABASE_REJECT_UNAUTHORIZED === 'true',
                    ca: process.env.DATABASE_CA ?? undefined,
                    key: process.env.DATABASE_KEY ?? undefined,
                    cert: process.env.DATABASE_CERT ?? undefined,
                }
                : undefined,
    },
} as DataSourceOptions);
