import { Body, Controller, Get, HttpCode, HttpStatus, Request, Post, Param } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { WebsiteService } from './website.service';
import { UpdateArticleDto } from './dto/update-article.dto';
import { AuthGuard } from '@nestjs/passport';
import { UseGuards } from '@nestjs/common';
import { CancelArticleDto } from './dto/cancel-article.dto';

@ApiTags('Website')
@Controller({
    path: 'website',
    version: '1',
})
export class WebsiteController {
    constructor(public websiteService: WebsiteService) { }

    @ApiOperation({ summary: '获取网站配置', description: '无限制' })
    @Get('')
    @HttpCode(HttpStatus.OK)
    async getWebsiteConfig() {
        return this.websiteService.getWebsiteConfig()
    }

    @ApiOperation({ summary: '获取文章浏览量', description: '无限制' })
    @Get('getArticleVisited/:id/:name')
    @HttpCode(HttpStatus.OK)
    async getVisitedTimes(@Param('id') id: string, @Param('name') name: string) {
        if (!id || !name) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Parameter error, id and name cannot be empty',
            };
        }
        return this.websiteService.getArticleVisitedTimes(id, name)
    }

    @ApiOperation({ summary: '获取文章点赞数、收藏数', description: '无限制' })
    @Get('getArticleInfo/:id/:name')
    @HttpCode(HttpStatus.OK)
    async getArticleInfo(@Param('id') id: string, @Param('name') name: string) {
        if (!id) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Parameter error, id cannot be empty',
            };
        }
        return this.websiteService.getArticleInfo(id, name)
    }

    @ApiOperation({ summary: '增加文章点赞数和收藏数', description: '无限制' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('addArticleLikeOrSubscribe')
    @HttpCode(HttpStatus.OK)
    async addArticleLike(@Request() request, @Body() updateArticleDto: UpdateArticleDto) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        if (!updateArticleDto.id || !updateArticleDto.name || !updateArticleDto.type) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Parameter error, id，name and type cannot be empty',
            };
        }

        if (updateArticleDto.type === 'like') {
            return this.websiteService.addArticleLike(updateArticleDto.id, updateArticleDto.name, userId)
        } else if (updateArticleDto.type === 'subscribe') {
            return this.websiteService.addSubscribe(updateArticleDto.id, updateArticleDto.name, userId)
        } else {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Article type error',
            };
        }
    }

    @ApiOperation({ summary: '取消点赞或收藏', description: '无限制' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('cancelArticleLikeOrSubscribe')
    @HttpCode(HttpStatus.OK)
    async cancelArticleLike(@Request() request, @Body() updateArticleDto: CancelArticleDto) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        if (!updateArticleDto.id || !updateArticleDto.type) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Parameter error, id，name and type cannot be empty',
            };
        }

        if (updateArticleDto.type === 'like') {
            return this.websiteService.cancelArticleLike(updateArticleDto.id, userId)
        } else if (updateArticleDto.type === 'subscribe') {
            return this.websiteService.cancelSubscribe(updateArticleDto.id, userId)
        } else {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'Acticel type error',
            };
        }
    }

    @ApiOperation({ summary: '发放产品激活码', description: '最多五次' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('signProductKey/:productName/:appId')
    @HttpCode(HttpStatus.OK)
    async signProductKey(@Request() request, @Param("productName") productName: "thino" | "templify", @Param("appId") appId: string) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        try {
            return await this.websiteService.signProductKey(productName, userId, appId)
        } catch (e) {
            return {
                code: HttpStatus.SEE_OTHER,
                message: "Unknown error, please contact pkmer" + e
            }
        }
    }

    @ApiOperation({ summary: '发放产品激活码', description: '最多10次' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('signProductKey')
    @HttpCode(HttpStatus.OK)
    async signProductKeyWithPost(@Request() request, @Body() body) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        const appId = body.appId
        const productName = body.productName

        if (!appId || !productName) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "appid or productName does not exist"
            }
        }

        try {
            return await this.websiteService.signProductKey(productName, userId, appId)

        } catch (e) {
            return {
                code: HttpStatus.SEE_OTHER,
                message: "Unknown error, please contact pkmer" + e
            }
        }
    }

    @ApiOperation({ summary: '列举产品激活码', description: '无限制' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('listProductKey/:productName')
    @HttpCode(HttpStatus.OK)
    async listProductKey(@Request() request, @Param("productName") productName: "thino" | "templify") {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        return await this.websiteService.getActivationRecord(productName, userId)
    }

    @ApiOperation({ summary: '删除产品激活码', description: '无限制' })
    @Post('deleteProductKey')
    @HttpCode(HttpStatus.OK)
    async deleteProductKey(@Body("userId") userId, @Body("appid") appid) {
        if (!userId || !appid) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }

        return await this.websiteService.deleteActivationRecord(appid, userId)
    }
}
