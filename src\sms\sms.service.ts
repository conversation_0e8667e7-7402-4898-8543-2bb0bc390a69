import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525';
import * as $OpenApi from '@alicloud/openapi-client';
import OpenApiUtil from '@alicloud/openapi-util';
import * as $Util from '@alicloud/tea-util';


@Injectable()
export class SmsService {
    private smsClient: Dysmsapi20170525
    constructor(private configService: ConfigService) {
        const accessKeyId = this.configService.get('sms.accessKeyId');
        const accessKeySecret = this.configService.get('sms.accessKeySecret');
        const config = new $OpenApi.Config({
            accessKeyId,
            accessKeySecret,
            endpoint: `dysmsapi.aliyuncs.com`
        })
        this.smsClient = new Dysmsapi20170525(config)
    }

    static createApiInfo(): $OpenApi.Params {
        let params = new $OpenApi.Params({
            // 接口名称
            action: "SendSms",
            // 接口版本
            version: "2017-05-25",
            // 接口协议
            protocol: "HTTPS",
            // 接口 HTTP 方法
            method: "POST",
            authType: "AK",
            style: "RPC",
            // 接口 PATH
            pathname: `/`,
            // 接口请求体内容格式
            reqBodyType: "json",
            // 接口响应体内容格式
            bodyType: "json",
        });
        return params;
    }

    async sendSms(PhoneNumbers: string, code: string) {
        let params = SmsService.createApiInfo()
        try {
            let queries = {
                PhoneNumbers,
                SignName: 'pkmer',
                TemplateCode: `SMS_274755155`,
                TemplateParam: `{\"code\":\"${code}\"}`
            }
            let runtime = new $Util.RuntimeOptions({});
            let request = new $OpenApi.OpenApiRequest({
                query: OpenApiUtil.query(queries),
            });
            // console.log(request)
            const result = await this.smsClient.callApi(params, request, runtime)
            return result;
        } catch (error) {
            throw new Error(`Failed to send SMS: ${error}`);
        }
    }
}
