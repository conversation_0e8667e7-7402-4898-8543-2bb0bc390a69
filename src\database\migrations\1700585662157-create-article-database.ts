import { MigrationInterface, QueryRunner } from "typeorm";

export class createArticleDatabase1700585662157 implements MigrationInterface {
    name = 'createArticleDatabase1700585662157'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`article\` (\`id\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`like\` int NOT NULL DEFAULT '0', \`subscribe\` int NOT NULL DEFAULT '0', \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`article\``);
    }

}
