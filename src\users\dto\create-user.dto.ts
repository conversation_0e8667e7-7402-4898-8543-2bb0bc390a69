import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../../roles/entities/role.entity';
import {
    IsEmail,
    IsNotEmpty,
    IsOptional,
    IsPhoneNumber,
    MinLength,
    Validate,
} from 'class-validator';
import { Status } from '../../statuses/entities/status.entity';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { FileEntity } from '../../files/entities/file.entity';
import { IsExist } from '../../utils/validators/is-exists.validator';

export class CreateUserDto {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value?.toLowerCase().trim())
    @IsNotEmpty()
    @Validate(IsNotExist, ['User'], {
        message: 'emailAlreadyExists',
    })
    @IsEmail()
    @IsOptional()
    email?: string | null;

    @ApiProperty({ example: '15895935801' })
    @Transform(({ value }) => value?.toLowerCase().trim())
    @IsNotEmpty()
    @Validate(IsNotExist, ['User'], {
        message: 'phoneAlreadyExists',
    })
    @IsPhoneNumber('CN')
    @IsOptional()
    phone?: string | null;

    @ApiProperty()
    @MinLength(6)
    password?: string;

    provider?: string;

    @ApiProperty()
    @IsOptional()
    openid?: string | null;

    @ApiProperty()
    @IsOptional()
    unionid?: string | null;

    socialId?: string | null;

    @ApiProperty({ example: 'John' })
    @IsNotEmpty()
    nickName: string | null;

    @ApiProperty({ type: () => FileEntity })
    @IsOptional()
    @Validate(IsExist, ['FileEntity', 'id'], {
        message: 'imageNotExists',
    })
    photo?: FileEntity | null;

    @ApiProperty({ type: Role })
    @Validate(IsExist, ['Role', 'id'], {
        message: 'roleNotExists',
    })
    role?: Role | null;

    @ApiProperty({ type: Status })
    @Validate(IsExist, ['Status', 'id'], {
        message: 'statusNotExists',
    })
    status?: Status;

    @ApiProperty({ example: false })
    @IsOptional()
    abroad?: boolean
}
