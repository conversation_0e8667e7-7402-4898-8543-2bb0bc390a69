import { MigrationInterface, QueryRunner } from "typeorm";

export class createShortcut1683353081766 implements MigrationInterface {
    name = 'createShortcut1683353081766'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`shortcut\` (\`id\` varchar(36) NOT NULL, \`creator_id\` varchar(255) NOT NULL, \`created_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`rowStatus\` enum ('NORMAL', 'ARCHIVED') NOT NULL DEFAULT 'NORMAL', \`title\` varchar(255) NOT NULL, \`payload\` varchar(255) NOT NULL, \`creatorId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`shortcut\` ADD CONSTRAINT \`FK_2dd50f7727262e1a4e47a0800cc\` FOREIGN KEY (\`creatorId\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`shortcut\` DROP FOREIGN KEY \`FK_2dd50f7727262e1a4e47a0800cc\``);
        await queryRunner.query(`DROP TABLE \`shortcut\``);
    }

}
