import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, Validate } from "class-validator";
import { IsExist } from "src/utils/validators/is-exists.validator";


export class CreateOrderDto {
    @ApiProperty({ example: '8e4b02e6-5932-4c66-bb9c-8437d4780777' })
    @Validate(IsExist, ['User', 'id'], { message: 'userNotExists' })
    @IsNotEmpty()
    creatorId: string;

    @ApiProperty({ example: 'order description' })
    description?: string;

    @ApiProperty({ example: '1511d94f-fg5d-4cc9-b68a-b1497c8fcbb7' })
    @Validate(IsExist, ['Goods', 'id'], { message: 'goodsNotExists' })
    @IsNotEmpty()
    goodsId: string;

    @ApiProperty({ example: 1 })
    @IsNotEmpty()
    goodsCount: number;

    // @ApiProperty({ example: 123.45 })
    // @IsNotEmpty()
    // price: number;

    // @ApiProperty({ example: 'pending' })
    // @IsNotEmpty()
    //'pending' | 'paid' | 'refunded' | 'canceled'
    // status: string;
}