import { MigrationInterface, QueryRunner } from "typeorm";

export class upgradePlugin1689172764231 implements MigrationInterface {
    name = 'upgradePlugin1689172764231'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`plugin_entity\` (\`id\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`repo\` varchar(255) NOT NULL, \`author\` varchar(255) NULL, \`banner\` varchar(255) NULL, \`category\` varchar(255) NULL, \`score\` int NULL, \`description\` text NULL, \`version\` varchar(255) NOT NULL, \`pluginUpdatedTime\` varchar(255) NOT NULL, \`updatedTime\` varchar(255) NOT NULL, \`downloadUrl\` varchar(255) NOT NULL, INDEX \`IDX_84d9b1c10f36b00669d5a66248\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_84d9b1c10f36b00669d5a66248\` ON \`plugin_entity\``);
        await queryRunner.query(`DROP TABLE \`plugin_entity\``);
    }

}
