import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsPhoneNumber, MinLength, Valida<PERSON> } from 'class-validator';
import { IsNotExist } from 'src/utils/validators/is-not-exists.validator';
import { Transform } from 'class-transformer';

export class AuthRegisterLoginDto {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @Validate(IsNotExist, ['User'], {
        message: 'emailAlreadyExists',
    })
    @IsEmail()
    @IsOptional()
    email?: string;

    @ApiProperty({ example: '14998783019' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @Validate(IsNotExist, ['User'], {
        message: 'phoneAlreadyExists',
    })
    @IsPhoneNumber('CN')
    @IsOptional()
    phone?: string

    @ApiProperty()
    @MinLength(6)
    password: string;

    @ApiProperty({ example: 'John' })
    @IsNotEmpty()
    nickName: string;

    @ApiProperty({ example: '134533' })
    @IsNotEmpty()
    captchar: string

    @IsOptional()
    unionid?: string

    @ApiProperty({ example: false })
    @IsOptional()
    abroad?: boolean

}
