import { Test, TestingModule } from '@nestjs/testing';
import { ForumController } from './forum.controller';
import { ForumService } from './forum.service';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';

describe('ForumController', () => {
    let controller: ForumController;
    let service: ForumService;

    const mockForumService = {
        getLatestTopics: jest.fn(),
        getTopicsByUser: jest.fn(),
        getCategories: jest.fn(),
        getTags: jest.fn(),
    };

    const mockConfigService = {
        get: jest.fn().mockReturnValue('https://forum.pkmer.net'),
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [ForumController],
            providers: [
                {
                    provide: ForumService,
                    useValue: mockForumService,
                },
                {
                    provide: ConfigService,
                    useValue: mockConfigService,
                },
            ],
        }).compile();

        controller = module.get<ForumController>(ForumController);
        service = module.get<ForumService>(ForumService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    describe('getLatestTopics', () => {
        it('should throw BadRequestException when apiKey is missing', async () => {
            await expect(
                controller.getLatestTopics('', 'clientId')
            ).rejects.toThrow(BadRequestException);
        });

        it('should throw BadRequestException when clientId is missing', async () => {
            await expect(
                controller.getLatestTopics('apiKey', '')
            ).rejects.toThrow(BadRequestException);
        });

        it('should call forumService.getLatestTopics with correct parameters', async () => {
            const mockResult = { success: true, data: { topics: [] } };
            mockForumService.getLatestTopics.mockResolvedValue(mockResult);

            const result = await controller.getLatestTopics('apiKey', 'clientId');

            expect(service.getLatestTopics).toHaveBeenCalledWith('apiKey', 'clientId');
            expect(result).toEqual(mockResult);
        });
    });

    describe('getTopicsByUser', () => {
        it('should throw BadRequestException when required parameters are missing', async () => {
            await expect(
                controller.getTopicsByUser('', 'clientId', 'username')
            ).rejects.toThrow(BadRequestException);
        });

        it('should call forumService.getTopicsByUser with correct parameters', async () => {
            const mockResult = { success: true, data: { topics: [] } };
            mockForumService.getTopicsByUser.mockResolvedValue(mockResult);

            const result = await controller.getTopicsByUser('apiKey', 'clientId', 'username');

            expect(service.getTopicsByUser).toHaveBeenCalledWith('apiKey', 'clientId', 'username');
            expect(result).toEqual(mockResult);
        });
    });
});
