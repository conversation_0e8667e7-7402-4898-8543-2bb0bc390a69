import { createHash } from "crypto";
import { getSignature, decrypt } from '@wecom/crypto';

const encodingAESKey = "aOEki6XTgNsK48fBgB2sOmsWcUH5FNFT38oTug4eH2Y"

/** 
    * 将token、timestamp、nonce三个参数进行字典序排序,将三个参数字符串拼接成一个字符串进行sha1加密。
    * 获得加密后的字符串可与signature对比,获得加密后的字符串可与signature对比
    * https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Access_Overview.html
    */
export function checkSignature(signature: string, timestamp: string, nonce: string, echostr: string, token: string): string {
    const arr = [token, timestamp, nonce].sort();
    const str = arr.join('');
    const sha1 = createHash('sha1');
    sha1.update(str);
    if (sha1.digest('hex') === signature) {
        return echostr;
    }
    return "";
}

export function checkThinoHelperSignature(signature: string, timestamp: string, nonce: string, echostr: string, token: string): string {

    const devsignature = getSignature(token, timestamp, nonce, echostr);
    const { message} = decrypt(encodingAESKey, echostr);
    if (devsignature === signature) {
        return message
    }
    return ""
}