import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { PaymentDto } from './dto/payment.dto';
import { CheckoutInfo, handleReplyCheckout, verify2Checkout } from './utils/2checkout.util';
import { CheckoutService } from './checkout.service';
import { logger } from 'nestjs-i18n';

@ApiTags('Payment')
@Controller({ path: 'payment', version: '1' })
export class PaymentController {
    constructor(private readonly paymentService: PaymentService, private readonly chekoutService: CheckoutService) { }

    @ApiOperation({ summary: '支付', description: '无限制' })
    @Post('pay')
    @HttpCode(HttpStatus.OK)
    async pay(@Body() paymentOptions: PaymentDto) {
        return this.paymentService.pay(paymentOptions.orderId, paymentOptions.returnURL);
    }

    @ApiOperation({ summary: '支付回调', description: '无限制' })
    @Post('callback')
    @HttpCode(HttpStatus.OK)
    async paymentCallback(@Body() body: any) {
        // console.log(body);
        const orderId = body.out_trade_no
        const orderStatus = body.trade_status


        // WAIT_BUYER_PAY：交易创建，等待买家付款。
        // TRADE_CLOSED：在指定时间段内未支付时关闭的交易或在交易完成全额退款成功时关闭的交易。
        // TRADE_SUCCESS：商家签约的产品支持退款功能的前提下，买家付款成功。
        // TRADE_FINISHED：商家签约的产品不支持退款功能的前提下，买家付款成功。或者，商家签约的产品支持退款功能的前提下，交易已经成功并且已经超过可退款期限。

        if (orderStatus === 'TRADE_SUCCESS' && body.total_amount) {
            await this.paymentService.handlePaymentSuccess(orderId, body.total_amount)
        } if (orderStatus === 'WAIT_BUYER_PAY') {
            await this.paymentService.handlePaymentPending(orderId)
        } else if (orderStatus === 'TRADE_CLOSED') {
            await this.paymentService.handlePaymentClosed(orderId)
        } else if (orderStatus === 'TRADE_FINISHED') {
            return 'TRADE_FINISHED'
        }
    }

    @ApiOperation({ summary: '2checkout支付', description: '无限制' })
    @Get('payCheckout')
    @HttpCode(HttpStatus.OK)
    async payCheckout() {
        return
    }

    @ApiOperation({ summary: '2checkout支付', description: '无限制' })
    @Post('payCheckout')
    @HttpCode(HttpStatus.OK)
    async payCheckoutCallback(@Body() paymentOptions: CheckoutInfo) {
        const isMessageFrom2Checkout = await verify2Checkout(paymentOptions)
        console.log(paymentOptions)
        if (!isMessageFrom2Checkout) {
            return {
                code: HttpStatus.BAD_GATEWAY
            }
        }
        if (String(paymentOptions.IPN_PID[0]) === "43440760") {
            await this.chekoutService.handleThinoPro(paymentOptions)
        } else {
            logger.error("处理2checkout失败，没有对应的处理逻辑", paymentOptions)
        }

        const result = handleReplyCheckout(paymentOptions)
        return result
    }
}
