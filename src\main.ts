import {
    ClassSerializerInterceptor,
    ValidationPipe,
    VersioningType,
} from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { useContainer } from 'class-validator';
import validationOptions from './utils/validation-options';
import express from 'express';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, { cors: true, abortOnError: false, logger: ["error", "warn"] });
    useContainer(app.select(AppModule), { fallbackOnErrors: true });
    const configService = app.get(ConfigService);
    app.enableShutdownHooks();

    app.setGlobalPrefix(configService.get('app.apiPrefix'), {
        exclude: ['/'],
    });
    app.enableVersioning({
        type: VersioningType.URI,
    });
    app.useGlobalPipes(new ValidationPipe(validationOptions));
    app.useGlobalInterceptors(
        new ClassSerializerInterceptor(app.get(Reflector)),
    );

    // 允许解析 raw body
    app.use(express.json({ 
        verify: (req: any, res, buf) => {
            req.rawBody = buf;
        }
    }));
    
    app.use(express.text({ 
        type: 'text/plain',
        verify: (req: any, res, buf) => {
            req.rawBody = buf;
        }
    }));

    const options = new DocumentBuilder()
        .setTitle('Pkmer API')
        .setDescription('The Pkmer API docs')
        .setVersion('1.0')
        .addTag('Users')
        .addTag('Auth')
        .addTag('WechatMp')
        .addTag('Files')
        .addTag('Widgets')
        .addTag('Payment')
        .addTag('Goods')
        .addTag('Orders')
        .addTag('Poll')
        .addTag('OSS')
        .addTag('Download')
        .addBearerAuth()
        .build();

    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup('docs', app, document);
    app.enableCors();
    await app.listen(configService.get('app.port'));
}

void bootstrap();
