
import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UsersService } from 'src/users/users.service';
import { Verify } from './entities/verify.entity';
import { Repository } from 'typeorm';
import { GoodsService } from 'src/goods/goods.service';
import * as jose from 'jose'
import { randomInt } from 'crypto';
import dayjs from 'dayjs';

@Injectable()
export class VerifyProductService {
    constructor(
        private usersService: UsersService,
        private goodsService: GoodsService,
        @InjectRepository(Verify) private verifyRepository: Repository<Verify>
    ) { }

    private async generateActivationCode(userId: string, productName: string, productId: string, expiredDate: string) {
        const salt = randomInt(1000)

        const payload = {
            type: productName,
            iss: "pkmer",
            userId: userId,
            i: salt,
            productId,
            expiredDate
        };
        const algorithm = "PS256";
        const pkcs8 = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

        const ecPrivateKey = await jose.importPKCS8(pkcs8, algorithm);

        const jws = await new jose.CompactSign(
            new TextEncoder().encode(JSON.stringify(payload))
        )
            .setProtectedHeader({ alg: "RS256" })
            .sign(ecPrivateKey);

        return jws
    }

    async signProductKey(userId: string, productId: string) {
        const verify = await this.verifyRepository.findOne({
            where: {
                creatorId: userId,
                goodsId: productId
            }
        })

        const product = await this.goodsService.findOne(productId)
        if (!product) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The product does not exist'
            }
        }

        if (verify) {
            try {
                const key = await this.generateActivationCode(userId, product.name, productId, dayjs(verify.expiredDate).format('YYYY-MM-DD'))
                if (!key) {
                    return {
                        code: HttpStatus.INTERNAL_SERVER_ERROR,
                        message: 'Internal server error, please contact pkmer'
                    }
                }

                return {
                    code: HttpStatus.OK,
                    message: 'success',
                    payload: {
                        key
                    }
                }

            } catch (e) {
                return {
                    code: HttpStatus.INTERNAL_SERVER_ERROR,
                    message: 'Internal server error, please contact pkmer' + e
                }

            }
        } else {
            await this.verifyRepository.save(this.verifyRepository.create({
                creatorId: userId,
                goodsId: productId,
                goodsName: product.name,
                purchased: false,
                expiredDate: dayjs().add(15, 'day').toDate(),
                activateCount: 1
            }))

            try {
                const key = await this.generateActivationCode(userId, product.name, productId, dayjs().add(15, 'day').format('YYYY-MM-DD'))
                if (!key) {
                    return {
                        code: HttpStatus.INTERNAL_SERVER_ERROR,
                        message: 'Internal server error, please contact pkmer'
                    }
                }

                return {
                    code: HttpStatus.OK,
                    message: 'success',
                    payload: {
                        key
                    }
                }

            } catch (e) {
                return {
                    code: HttpStatus.INTERNAL_SERVER_ERROR,
                    message: 'Internal server error, please contact pkmer' + e
                }

            }
        }
    }

    async getVerifyInfo(userId: string, productId: string) {
        const verify = await this.verifyRepository.findOne({
            where: {
                creatorId: userId,
                goodsId: productId
            }
        })

        if (!verify) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'You need activate the product first',
            }
        }

        return {
            code: HttpStatus.OK,
            message: 'success',
            payload: {
                productName: verify.goodsName,
                purchased: verify.purchased,
                expiredDate: verify.expiredDate,
                activateCount: verify.activateCount,
                createAt: verify.createdAt,
            }
        }
    }


    async verifyProduct(userId: string, goodsId: string) {
        const user = await this.usersService.findOne({
            id: userId
        })

        if (!user) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist',
            }
        }

        const verify = await this.verifyRepository.findOne({
            where: {
                creatorId: userId,
                goodsId
            }
        })

        if (!verify) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'You need activate the product first',
            }
        }

        await this.verifyRepository.update(verify.id, {
            activateCount: verify.activateCount + 1
        })


        return {
            code: HttpStatus.OK,
            message: 'success',
            payload: {
                productName: verify.goodsName,
                purchased: verify.purchased,
                expiredDate: verify.expiredDate,
                activateCount: verify.activateCount,
                createAt: verify.createdAt,
            }
        }
    }

    async makeProductPurchased(userId: string, goodsId: string) {
        const verify = await this.verifyRepository.findOne({
            where: {
                creatorId: userId,
                goodsId
            }
        })

        const product = await this.goodsService.findOne(goodsId)
        if (!product) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The product does not exist'
            }
        }

        if (!verify) {
            return await this.verifyRepository.save(this.verifyRepository.create({
                creatorId: userId,
                goodsId,
                goodsName: product.name,
                purchased: true,
                expiredDate: dayjs().add(15, 'day').toDate(),
                activateCount: 0
            }))
        }

        return await this.verifyRepository.update(verify.id, {
            purchased: true
        })

    }

}
