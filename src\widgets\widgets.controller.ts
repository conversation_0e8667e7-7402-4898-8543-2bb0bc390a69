import { Controller, DefaultValuePipe, Get, HttpCode, HttpStatus, ParseIntPipe, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { infinityPagination } from 'src/utils/infinity-pagination';
import { WidgetsService } from './widgets.service';

@ApiTags('Widgets')
@Controller({ path: 'widgets', version: '1' })
export class WidgetsController {
    constructor(private readonly widgetsService: WidgetsService) { }

    @ApiOperation({ summary: '获取挂件信息', description: '无限制' })
    @Get()
    @HttpCode(HttpStatus.OK)
    async findAll(@Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,) {

        if (limit > 25) {
            limit = 25
        }

        return infinityPagination(await this.widgetsService.findManyWithPagination({ page, limit }), { page, limit })
    }
}
