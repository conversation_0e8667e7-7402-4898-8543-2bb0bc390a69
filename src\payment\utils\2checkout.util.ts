import { createHmac } from "crypto";

const usedHashAlgorithm: string = 'sha256';
const secretKey: string = "X?Tc%k[14OD3sA#Qb=l)";

function serializeArray(checkoutInfo: CheckoutInfo | string[]): string {
    let ret_value = "";

    for (const [key, val] of Object.entries(checkoutInfo)) {
        if (['HASH', 'SIGNATURE_SHA2_256', 'SIGNATURE_SHA3_256'].includes(key)) {
            continue;
        }

        if (Array.isArray(val)) {
            ret_value += serializeArray(val);
        } else {
            ret_value += `${val.toString().length}${val}`;
        }
    }
    return ret_value;
}

// {
//   SALEDATE: '2024-03-11 12:53:08',
//   REFNO: '230136464',
//   REFNOEXT: '',
//   ORDERNO: '48',
//   ORDERSTATUS: 'PAYMENT_AUTHORIZED',
//   PAYMETHOD: 'Visa/MasterCard',
//   ZIPCODE: '31234',
//   COUNTRY: 'Hong Kong',
//   PHONE: '',
//   CUSTOMEREMAIL: '<EMAIL>',
//   COMPLETE_DATE: '0000-00-00 00:00:00',
//   CURRENCY: 'HKD',
//   IPN_PID: [ '43440760' ],
//   IPN_PNAME: [ 'Thino' ],
//   IPN_PCODE: [ '77HMHIT9BP' ],
//   IPN_EXTERNAL_REFERENCE: [ '652cdg3w-sf33-4452-a120-b9b7135165df' ],
//   IPN_INFO: [ '' ],
//   IPN_QTY: [ '1' ],
//   IPN_PRICE: [ '155.62' ],
//   IPN_VAT: [ '0.00' ],
//   IPN_VER: [ 'Pro' ],
//   IPN_DISCOUNT: [ '0.00' ],
//   MESSAGE_ID: '254929203178',
//   IPN_DELIVEREDCODES: [ '' ],
//   IPN_TOTAL: [ '155.62' ],
//   IPN_TOTALGENERAL: '155.62',
//   IPN_SHIPPING: '0.00',
//   IPN_SHIPPING_TAX: '0.00',
//   IPN_COMMISSION: '14.0294',
//   IPN_CUSTOM_TEXT: [ 'UserID' ],
//   IPN_CUSTOM_VALUE: [ '9addaa3e-d4df-4472-a260-b9b7f3b40e4a' ],
//   TEST_ORDER: '1',
//   IPN_DATE: '20240311125316',
//   SIGNATURE_SHA2_256: 'a829feddf306d63b8c4ad86bc6840ed8c0b97f29e924473b5ec64f6027e157dd'
// }

export interface CheckoutInfo {
    SALEDATE: Date
    ORDERSTATUS: "PENDING_APPTOVAL" | "COMPLETE"
    CUSTOMEREMAIL: string,
    IPN_PNAME: "THINO" | "TEMPLIFY"
    IPN_PID: string[]
    IPN_DATE: string
    COUNTRY: string
    IPN_TOTALGENERAL: string
    IPN_CUSTOM_TEXT: string[]
    IPN_CUSTOM_VALUE: string[]
    TEST_ORDER: "1" | "0"
    SIGNATURE_SHA2_256?: string
    SIGNATURE_SHA3_256?: string
    ORDERNO: string
    CURRENCY: string
    IPN_EXTERNAL_REFERENCE: string[]
}

export function verify2Checkout(checkoutInfo: CheckoutInfo): boolean {
    const signature_sha2 = checkoutInfo.SIGNATURE_SHA2_256 || '';
    const signature_sha3 = checkoutInfo.SIGNATURE_SHA3_256 || '';

    const stringForHash = serializeArray(checkoutInfo);
    const computedHash = createHmac(usedHashAlgorithm, secretKey).update(stringForHash).digest("hex");

    let validHash = false;

    switch (usedHashAlgorithm) {
        case "sha256":
            if (computedHash === signature_sha2) {
                validHash = true;
            }
            break;
        case "sha3-256":
            if (computedHash === signature_sha3) {
                validHash = true;
            }
            break;
    }

    if (!validHash) {
        // send email
        return false;
    }
    return true
}

export function handleReplyCheckout(body: CheckoutInfo) {
    const responseDate = new Date().toISOString().replace(/[^0-9]/g, '');

    const arrayForResponseHash = [
        body.IPN_PID[0],
        body.IPN_PNAME[0],
        body.IPN_DATE,
        responseDate
    ];

    const stringForResponseHash = serializeArray(arrayForResponseHash);

    let responseString = '';

    switch (usedHashAlgorithm) {
        case "sha256":
            const responseHash_sha256 = createHmac('sha256', secretKey).update(stringForResponseHash).digest("hex");
            responseString = `<sig algo="sha256" date="${responseDate}">${responseHash_sha256}</sig>`;
            break;
        case "sha3-256":
            const responseHash_sha3_256 = createHmac('sha3-256', secretKey).update(stringForResponseHash).digest("hex");
            responseString = `<sig algo="sha3-256" date="${responseDate}">${responseHash_sha3_256}</sig>`;
            break;
    }
    return responseString
}