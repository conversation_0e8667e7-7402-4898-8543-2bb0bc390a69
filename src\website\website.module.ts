import { Module } from '@nestjs/common';
import { WebsiteService } from './website.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Website } from './entities/website.entity';
import { WebsiteController } from './website.controller';
import { Article } from './entities/article.entity';
import { UsersModule } from 'src/users/users.module';
import { ActivationRecord } from './entities/activation-record.entity';

@Module({
    imports: [TypeOrmModule.forFeature([Website, Article, ActivationRecord]), UsersModule],
    providers: [WebsiteService],
    exports: [WebsiteService],
    controllers: [WebsiteController]
})
export class WebsiteModule { }
