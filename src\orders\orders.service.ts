
import { HttpStatus, Injectable } from '@nestjs/common';
import { Order } from './entities/order.entity';
import type { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { EntityCondition } from 'src/utils/types/entity-condition.type';
import { IPaginationOptions } from 'src/utils/types/pagination-options';
import { GoodsService } from 'src/goods/goods.service';
import { CheckoutInfo } from 'src/payment/utils/2checkout.util';
import { CheckoutOrder } from './entities/checkout-order.entity';
import { isEmail, isPhoneNumber } from 'class-validator';
import { UsersService } from 'src/users/users.service';

//这是nestjs的订单服务模块，用于定义订单服务，比如订单的增删改查，订单的支付，订单的退款等等
@Injectable()
export class OrdersService {
    //导入用户实体类，订单实体类
    constructor(
        @InjectRepository(Order)
        private ordersRepository: Repository<Order>,
        @InjectRepository(CheckoutOrder)
        private checkoutOrderReposotory: Repository<CheckoutOrder>,
        private goodsService: GoodsService,
        private usersService: UsersService
    ) { }
    //这是一个订单的增加方法，用于增加一个订单
    async createOrder(orderProfileDto: CreateOrderDto) {
        //根据参数，创建一个订单，然后保存到订单数据库
        const goods = await this.goodsService.findOne(orderProfileDto.goodsId)
        const goodsPrice = goods.price

        return this.ordersRepository.save(this.ordersRepository.create({ status: 'pending', amount: goodsPrice * orderProfileDto.goodsCount, ...orderProfileDto }));
    }

    async createCheckoutOrder(checkoutInfo: CheckoutInfo, userId: string) {
        const isOrderExist = await this.checkoutOrderReposotory.exist({
            where: {
                checkoutOrderId: checkoutInfo.ORDERNO
            }
        })
        const goodsId = checkoutInfo.IPN_EXTERNAL_REFERENCE[0]
        const goodsPrice = checkoutInfo.IPN_TOTALGENERAL


        if (isOrderExist || checkoutInfo.ORDERSTATUS !== "COMPLETE") {
            return
        }

        return await this.checkoutOrderReposotory.save(this.checkoutOrderReposotory.create({
            status: "paid",
            goodsId,
            checkoutOrderId: checkoutInfo.ORDERNO,
            email: checkoutInfo.CUSTOMEREMAIL,
            amount: Number(goodsPrice),
            country: checkoutInfo.COUNTRY,
            goodsCount: 1,
            currency: checkoutInfo.CURRENCY,
            goodsName: checkoutInfo.IPN_PNAME,
            creatorId: userId
        }))
    }

    //这是一个订单的删除方法，用于删除一个订单
    async softDeleteOrder(id: string) {
        //这里是删除订单的具体实现
        await this.ordersRepository.softDelete(id);
    }

    async hardDeleteOrder(id: string) {
        //这里是删除订单的具体实现
        await this.ordersRepository.delete(id);
    }

    //这是一个订单的修改方法，用于用户修改一个订单
    async updateOrder(id: string, updateOrderDto: UpdateOrderDto) {
        //这里是修改订单的具体实现
        return this.ordersRepository.save(this.ordersRepository.create({
            id,
            ...updateOrderDto
        })
        )
    }

    //单独一个订单状态的修改方法，用于后台修改订单状态
    async updateOrderStatus(id: string, status: 'pending' | 'paid' | 'refunded' | 'canceled') {
        return this.ordersRepository.save(this.ordersRepository.create({
            id,
            status
        }))
    }

    //这是一个订单的查询方法，用于查询一个订单
    async findOrder(fields: EntityCondition<Order>) {
        //这里是查询订单的具体实现
        const orders = await this.ordersRepository.findOne({
            where: fields,
        })
        if (orders) {
            return orders
        } else {
            const checkoutOrders = await this.checkoutOrderReposotory.findOne({
                where: fields
            })
            return checkoutOrders
        }

    }

    async findOrders(fields: EntityCondition<Order>) {
        //这里是查询订单的具体实现
        return this.ordersRepository.find({
            where: fields,
        })
    }

    //这是一个订单的查询方法，用于查询一个用户的所有订单
    async findUserOrders(paginationOptions: IPaginationOptions, creatorId: string) {

        //这里是查询用户订单的具体实现
        const orders = await this.ordersRepository.find({
            skip: (paginationOptions.page - 1) * paginationOptions.limit,
            take: paginationOptions.limit,
            where: {
                creatorId,
                status: "paid"
            }
        })

        const checkoutOrders = await this.checkoutOrderReposotory.find({
            skip: (paginationOptions.page - 1) * paginationOptions.limit,
            take: paginationOptions.limit,
            where: {
                creatorId,
                status: "paid"
            }
        })

        return [...orders, ...checkoutOrders]
    }

    async isZKVaultUser(adminSecret: string, query: string) {
        if (adminSecret !== "xycpbgad") {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "非管理员，禁止访问"
            }
        }

        console.log(query, isPhoneNumber(query), isEmail(query))

        if (!isEmail(query)) {
            const customer = await this.usersService.findOne({
                phone: query
            })

            if (!customer) {
                return {
                    code: HttpStatus.NOT_FOUND,
                    message: "未找到该用户，请检查查询参数是否正确"
                }
            }

            return await this.ordersRepository.find({
                where: {
                    goodsId: "dc2cbf6f-f243-49e7-b849-2c687254eff5",
                    status: "paid",
                    creatorId: customer.id
                }
            })
        } else {
            const customer = await this.usersService.findOne({
                email: query
            })

            if (!customer) {
                return {
                    code: HttpStatus.NOT_FOUND,
                    message: "未找到该用户，请检查查询参数是否正确"
                }
            }

            return await this.ordersRepository.find({
                where: {
                    goodsId: "dc2cbf6f-f243-49e7-b849-2c687254eff5",
                    status: "paid",
                    creatorId: customer.id
                }
            })
        }

    }
}
