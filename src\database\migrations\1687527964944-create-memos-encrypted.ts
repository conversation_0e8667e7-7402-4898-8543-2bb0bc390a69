import { MigrationInterface, QueryRunner } from "typeorm";

export class createMemosEncrypted1687527964944 implements MigrationInterface {
    name = 'createMemosEncrypted1687527964944'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`memo\` ADD \`encrypted\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`memo\` DROP COLUMN \`encrypted\``);
    }

}
