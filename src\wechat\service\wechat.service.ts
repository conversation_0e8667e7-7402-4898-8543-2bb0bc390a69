import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { AccountAccessTokenResult, WechatMessage, ReplayWechatMessageXML } from '../interfaces/wechat.interface';
import { firstValueFrom } from 'rxjs';
import { UsersService } from 'src/users/users.service';
import { RoleEnum } from 'src/roles/roles.enum';
import { StatusEnum } from 'src/statuses/statuses.enum';
import { Role } from 'src/roles/entities/role.entity';
import { Status } from 'src/statuses/entities/status.entity';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis';
import { WebsiteService } from 'src/website/website.service';
import WeChatMsgQueue from '../utils/wechatMsgQueue';

// 微信订阅号

@Injectable()
export class WechatService {
    public wechatMessageQueue = new WeChatMsgQueue(9999);

    constructor(
        private configService: ConfigService,
        private httpService: HttpService,
        private usersService: UsersService,
        private websiteService: WebsiteService,
        @InjectRedis() private readonly redisClient: Redis,
    ) { }

    async getWechatLoginUrl(isWeChatBrowser: boolean) {
        if (isWeChatBrowser) {
            const appId = this.configService.get('wechatOpen.mpAppId');
            const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=https://pkmer.cn/products/wechatLogin&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
            return url;
        } else {
            const appId = this.configService.get('wechatOpen.appId');
            const url = `https://open.weixin.qq.com/connect/qrconnect?appid=${appId}&redirect_uri=https://pkmer.cn/products/wechatLogin&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect`
            return url;
        }

    }

    async getAccessToken(code: string, isWeChatBrowser: boolean): Promise<AccountAccessTokenResult> {
        const appId = isWeChatBrowser ? this.configService.get('wechatOpen.mpAppId') : this.configService.get('wechatOpen.appId');
        const secret = isWeChatBrowser ? this.configService.get('wechatOpen.mpSecret') : this.configService.get('wechatOpen.secret');
        const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${secret}&code=${code}&grant_type=authorization_code`;
        const { data } = await firstValueFrom(
            this.httpService.get<AccountAccessTokenResult>(url).pipe(),
        );
        return data;
        //         return {
        //   access_token: '68_-IR5xoS24R9EmM9eR7gq-rAKyGhPmT8O8Us_gs7kewTIlWgQ6qM44AqSxxRO-Q0Z9_Q0X0iLBw6sG5TrUk5iiFkYZ26xxH_48TS6GCFdIZ8',
        //   expires_in: 7200,
        //   refresh_token: '68_0JPLngUbTfT4sHauZ4IAAY-8jbu4RoJZAjECW0S--drH4JhKvDlisO-D8zWIQLmr3I70bHNZOM6RT7SikE7AsRmEVbDeTHbAO7iPyCPNj2o',
        //   openid: 'ojsHb6fnO2u1M7MgAaSTImQi5dTc',
        //   scope: 'snsapi_login',
        //   unionid: 'oEkT66YQrhdV5_01iH6CZjrVTtTw'
        // }
    }

    async getUserSubscibeUrl(): Promise<string> {
        let accessToken = await this.redisClient.get('wechatMpAccessToken');
        if (!accessToken) {
            const getAccessToken = await fetch(`https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${this.configService.get('wechatOpen.subscribeAppId')}&secret=${this.configService.get('wechatOpen.subscribeSecret')}`, {
                method: 'GET'
            });

            const getAccessTokenResult = await getAccessToken.json();
            accessToken = getAccessTokenResult.access_token;
            await this.redisClient.set('wechatMpAccessToken', accessToken, 'EX', 7200);
        }


        let ticket = await this.redisClient.get('wechatTicketUrl')
        if (!ticket) {
            const getTikectUrl = `https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=${accessToken}`;
            const getTikect = await fetch(getTikectUrl, {
                method: 'POST',
                body: JSON.stringify({
                    "expire_seconds": 604800,
                    "action_name": "QR_SCENE",
                    "action_info": {
                        "scene": {
                            "scene_id": 1
                        }
                    }
                })
            })
            const getTikectResult = await getTikect.json();
            await this.redisClient.set('wechatTicketUrl', getTikectResult.ticket, 'EX', getTikectResult.expire_seconds);
            ticket = getTikectResult.ticket;
        }


        return `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${ticket}`;
    }


    /**
     * 微信公众号 Access token
     * 有效期 2h
     * https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Get_access_token.html
     */
    async getWechatMpAccessToken(): Promise<AccountAccessTokenResult> {
        // TODO: 保存，只有过期时请求
        const appId = this.configService.get('wechatOpen.appId');
        const secret = this.configService.get('wechatOpen.secret');
        const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${secret}`;
        const { data } = await firstValueFrom(
            this.httpService.get<AccountAccessTokenResult>(url).pipe(),
        );
        return data;
        // {"access_token":"ACCESS_TOKEN","expires_in":7200}
        // {"errcode":40013,"errmsg":"invalid appid"}
    }

    /*
    * 微信公众号用户关注事件处理
    * 这里能获得用户的openid和unionid，但是取消关注后获取不到unionid，所以这里保存两个
    */
    private async handleUserSubscribeEvent(message: WechatMessage): Promise<void> {
        let accessToken = await this.redisClient.get('wechatMpAccessToken');
        if (!accessToken) {
            const getAccessToken = await fetch(`https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${this.configService.get('wechatOpen.subscribeAppId')}&secret=${this.configService.get('wechatOpen.subscribeSecret')}`, {
                method: 'GET'
            });

            const getAccessTokenResult = await getAccessToken.json();
            accessToken = getAccessTokenResult.access_token;
            await this.redisClient.set('wechatMpAccessToken', accessToken, 'EX', 7200);
        }

        const getUserInfoUrl = `https://api.weixin.qq.com/cgi-bin/user/info?access_token=${accessToken}&openid=${message.FromUserName}&lang=zh_CN`;
        const getUserInfo = await fetch(getUserInfoUrl, {
            method: 'GET'
        })
        const getUserInfoResult = await getUserInfo.json();
        //         {
        //               subscribe: 1,
        //               openid: 'oCEyT6hUChn46oSiNr1EVlBtdeYY',
        //               nickname: '',
        //               sex: 0,
        //               language: 'zh_CN',
        //               city: '',
        //               province: '',
        //               country: '',
        //               headimgurl: '',
        //               subscribe_time: 1693536628,
        //               unionid: 'oEkT66VlVuYfVk2DGdUPt4dbhWM4',
        //               remark: '',
        //               groupid: 0,
        //               tagid_list: [],
        //               subscribe_scene: 'ADD_SCENE_SEARCH',
        //               qr_scene: 0,
        //               qr_scene_str: ''
        //         }
        const unionid = getUserInfoResult.unionid;

        if (!unionid) throw new BadRequestException('unionid is null');

        const wechatUser = await this.usersService.findOne({
            unionid

        });

        if (wechatUser && wechatUser.openid) {
            await this.usersService.update(wechatUser.id, {
                subscribeWechat: true,
            });
        } else if (wechatUser && !wechatUser.openid) {
            await this.usersService.update(wechatUser.id, {
                openid: message.FromUserName,
                unionid,
                subscribeWechat: true,
            });
        } else {
            const user = await this.usersService.create({
                openid: message.FromUserName,
                unionid,
                nickName: message.FromUserName,
                provider: "wechatSubscribe",
                role: {
                    id: RoleEnum.user,
                } as Role,
                status: {
                    id: StatusEnum.active,
                } as Status,
            });

            await this.usersService.update(user.id, {
                subscribeWechat: true,
            });
        }
    }

    private async handleUserUnsubscribeEvent(message: WechatMessage): Promise<void> {

        const wechatUser = await this.usersService.findOne({
            openid: message.FromUserName

        });
        if (wechatUser && wechatUser.subscribeWechat) {
            await this.usersService.update(wechatUser.id, {
                subscribeWechat: false,
            });
        }
    }

    async unbindWechatUser(userId: string) {
        const user = await this.usersService.findOne({
            id: userId,
        });

        if (user.phone || user.email) {
            return this.usersService.unbindWechatUnionId(userId)
        }

        return {
            code: 400,
            message: '请先绑定邮箱或者手机号',
        }

    }

    // 开发者需要对用户消息在5秒内立即做出回应
    async wechatMpMessageService(message: WechatMessage): Promise<ReplayWechatMessageXML> {
        // 压入处理队列
        this.wechatMessageQueue.enqueue(message);
        this.weChatMsgQueueConsumer(); // 触发消费队列
        // 同步 回复
        let reply = "";
        switch (message.MsgType) {
            case "text":
                if (message.Content.match(/搜 .*/)) {
                    const keyWord = message.Content.split(" ")[1];
                    const form = new FormData();
                    form.append("content", keyWord);

                    const res = await fetch('http://wx.pkmer.cn/httpapi/test.php', {
                        method: "POST",
                        body: form
                    });
                    const data = await res.text();
                    const tipData = data.match(/\[.*?\]\(.*?\)/g)
                    if (!tipData) {
                        reply = `没有找到相关内容,请尝试其他关键词,或者去https://pkmer.cn/search?key=${encodeURI(keyWord)} 全文搜索`
                        break;
                    }
                    //将匹配到的内容替换为 1. [xxx](url)\n 2. [xxx](url)\n的格式
                    const formData = tipData.map((item, index) => {
                        return `${index + 1}. ${item}\n`
                    })

                    reply = formData.join("") + '\n也可试试全文检索 https://pkmer.cn/search?key=' + encodeURI(keyWord)
                    break;
                }
                const replyTemplate = await this.websiteService.getWechatReply()
                if (!replyTemplate) {
                    reply = "网站维护不易，有任何bug欢迎加群反馈： https://mp.weixin.qq.com/s/gwaQHFw7wri2wXjEI85fbA，也欢迎各位加入群聊一起交流，共同进步"
                    break;
                }

                const templateIndex = replyTemplate.findIndex((template) => template.name === message.Content)
                if (templateIndex != -1) {
                    reply = replyTemplate[templateIndex].content
                } else {
                    reply = "网站维护不易，有任何bug欢迎加群反馈： https://mp.weixin.qq.com/s/gwaQHFw7wri2wXjEI85fbA，也欢迎各位加入群聊一起交流，共同进步"
                }
                break;
            case "image":
                reply = message.PicUrl;
                break;
            case "voice":
                reply = message.Recognition; // 需要开启语音识别
                break;
            case "video":
                reply = "不支持的消息类型！"
                break;
            case "shortvideo":
                reply = "不支持的消息类型！"
                break;
            case "location":
                break;
            case "link":
                reply = message.Url;
                break;
            case "event":
                // 订阅事件处理
                if (message.Event === "subscribe") {
                    try {
                        await this.handleUserSubscribeEvent(message);
                    } catch (e) {
                        console.log('订阅公众号事件处理错误', e);
                    }
                    reply = `欢迎关注PKMer！PKMer官方网站：https://pkmer.cn
网站正在上升发展中，文章不断更新，获取PKMer最新资讯： https://mp.weixin.qq.com/s/IxwktZE2_9UN-F5jzeTWXw
网站维护不易，有任何bug欢迎加群反馈： https://mp.weixin.qq.com/s/OzMLlVzyBPXCimEbg5AezQ
——————
公众号提供了网站文章搜索功能，回复“搜 关键词”即可搜索相关文章，例如“搜 知识管理”，注意不要带引号，搜和关键词之间要加一个空格`;
                }

                if (message.Event === "unsubscribe") {
                    try {
                        await this.handleUserUnsubscribeEvent(message);
                    } catch (e) {
                        console.log('取消订阅公众号事件处理错误', e);
                    }
                    reply = "取消关注";
                }
                break;
            default:
                reply = "网站维护不易，有任何bug欢迎加群反馈： https://mp.weixin.qq.com/s/gwaQHFw7wri2wXjEI85fbA，也欢迎各位加入群聊一起交流，共同进步";
                break;
        }
        // 返回文字类型
        return {
            xml: {
                ToUserName: message.FromUserName, // 来自谁 Openid
                FromUserName: this.configService.get('wechatOpen.mpId'), // 开发者微信号
                CreateTime: Math.floor(new Date().getTime() / 1000),
                MsgType: 'text',
                Content: reply
            }
        };
    }

    async weChatMsgQueueConsumer() {
        // example
        setTimeout(() => {
            this.wechatMessageQueue.dequeue();
            // let item = this.wechatMessageQueue.dequeue();
            // console.log(item);
        }, 5000);
    }
}


