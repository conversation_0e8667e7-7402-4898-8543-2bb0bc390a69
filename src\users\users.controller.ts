import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    UseGuards,
    Query,
    DefaultValuePipe,
    ParseIntPipe,
    HttpStatus,
    HttpCode,
    SerializeOptions,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Roles } from 'src/roles/roles.decorator';
import { RoleEnum } from 'src/roles/roles.enum';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from 'src/roles/roles.guard';
import { infinityPagination } from 'src/utils/infinity-pagination';

@ApiBearerAuth()
@Roles(RoleEnum.admin)
@UseGuards(AuthGuard('jwt'), RolesGuard)
@ApiTags('Users')
@Controller({
    path: 'users',
    version: '1',
})
export class UsersController {
    constructor(private readonly usersService: UsersService) { }

    @ApiOperation({ summary: '管理员用于后台创建用户', description: '管理员权限' })
    @SerializeOptions({
        groups: ['admin'],
    })
    @Post()
    @HttpCode(HttpStatus.CREATED)
    create(@Body() createProfileDto: CreateUserDto) {
        return this.usersService.create(createProfileDto);
    }

    @ApiOperation({ summary: '管理员用于获取用户数据', description: '管理员权限' })
    @SerializeOptions({
        groups: ['admin'],
    })
    @Get()
    @HttpCode(HttpStatus.OK)
    async findAll(
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    ) {
        if (limit > 50) {
            limit = 50;
        }

        return infinityPagination(
            await this.usersService.findManyWithPagination({
                page,
                limit,
            }),
            { page, limit },
        );
    }

    @ApiOperation({ summary: '管理员用于获取单个用户数据', description: '管理员权限' })
    @SerializeOptions({
        groups: ['admin'],
    })
    @Get(':id')
    @HttpCode(HttpStatus.OK)
    findOne(@Param('id') id: string) {
        return this.usersService.findOne({ id });
    }

    @ApiOperation({ summary: '管理员用于更新用户数据', description: '管理员权限' })
    @SerializeOptions({
        groups: ['admin'],
    })
    @Patch(':id')
    @HttpCode(HttpStatus.OK)
    update(@Param('id') id: string, @Body() updateProfileDto: UpdateUserDto) {
        return this.usersService.update(id, updateProfileDto);
    }

    @ApiOperation({ summary: '管理员用于删除用户数据', description: '管理员权限' })
    @Delete(':id')
    remove(@Param('id') id: string) {
        return this.usersService.softDelete(id);
    }
}
