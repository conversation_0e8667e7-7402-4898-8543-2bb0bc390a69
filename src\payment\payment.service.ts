import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import AlipaySdk from 'alipay-sdk';
import { GoodsService } from 'src/goods/goods.service';
import { OrdersService } from 'src/orders/orders.service';
import { UsersService } from 'src/users/users.service';
import { VerifyProductService } from 'src/verify/verify-product.service';

@Injectable()
export class PaymentService {
    private alipay: AlipaySdk;
    constructor(
        private configService: ConfigService,
        private goodsService: GoodsService,
        private ordersService: OrdersService,
        private usersService: UsersService,
        private verifyProductService: VerifyProductService
    ) {
        // console.log(this.configService.get('payment'))
        this.alipay = new AlipaySdk({
            appId: this.configService.get('payment.appId'),
            privateKey: this.configService.get('payment.privateKey'),
            alipayPublicKey: this.configService.get('payment.alipayPublicKey'),
        });
    }

    async pay(orderId: string, returnURL) {
        if (!returnURL) {
            returnURL = this.configService.get('payment.returnURL');
        }
        const order = (await this.ordersService.findOrder({ id: orderId }));
        if (!order) {
            return '订单不存在';
        }
        const goods = await this.goodsService.findOne(order.goodsId);
        if (!goods) return '商品不存在';

        const user = await this.usersService.findOne({ id: order.creatorId });

        if (!user) {
            return {
                code: HttpStatus.NOT_FOUND,
                message: '用户不存在，请联系网站管理员',
            }
        }

        if (!user.email && !user.phone) {
            return {
                code: HttpStatus.FORBIDDEN,
                message: '用户未绑定邮箱或手机号',
            }
        }


        const result = await this.alipay.pageExec(
            'alipay.trade.page.pay',
            {
                method: 'GET',
                charset: 'utf-8',
                signType: 'RSA2',
                bizContent: {
                    out_trade_no: orderId,
                    product_code: 'FAST_INSTANT_TRADE_PAY',
                    total_amount: order.goodsCount * goods.price,
                    subject: goods.name,
                    timeout_express: '5m',
                },
                notify_url: `${this.configService.get('app.backendDomain')}/api/v1/payment/callback`,
                returnURL,
            },
        );
        // console.log(result)
        return result;
    }

    private async changeOrderStatus(orderId: string, status: 'pending' | 'paid' | 'refunded' | 'canceled') {
        const order = await this.ordersService.findOrder({ id: orderId });
        if (!order) {
            return '订单不存在';
        }
        const changeOrderStatus = await this.ordersService.updateOrderStatus(orderId, status);
        if (!changeOrderStatus) {
            throw Error('订单状态修改失败')
        }
        return true;
    }



    private async handleSuscribe(orderId: string, goodsName: string, userId: string, goodsCount: number) {
        const user = await this.usersService.findOne({ id: userId });

        // 分别处理订单 insider vip的订阅
        if (goodsName === 'PkmerMonthlySuscribe') {
            const oldExpirationDate = user.expirationDate
            let newExpirationDate
            if (!oldExpirationDate) {
                newExpirationDate = new Date();
                newExpirationDate.setMonth(newExpirationDate.getMonth() + 1 * goodsCount);
            } else {
                newExpirationDate = new Date(oldExpirationDate);
                newExpirationDate.setMonth(new Date(oldExpirationDate).getMonth() + 1 * goodsCount);
            }
            await this.usersService.changeUserType(userId, 'insider', newExpirationDate);
            await this.changeOrderStatus(orderId, 'paid');
        } else if (goodsName === 'PkmerYearlySuscribe') {
            const oldExpirationDate = user.expirationDate
            let newExpirationDate;
            if (!oldExpirationDate) {
                newExpirationDate = new Date();
                newExpirationDate.setFullYear(new Date().getFullYear() + 1 * goodsCount);
            } else {
                newExpirationDate = new Date(oldExpirationDate);
                newExpirationDate.setFullYear(new Date(oldExpirationDate).getFullYear() + 1 * goodsCount);
            }
            await this.usersService.changeUserType(userId, 'insider', newExpirationDate);
            await this.changeOrderStatus(orderId, 'paid');
        } else if (goodsName === 'ThinoWebMonthlySuscribe') {
            const oldExpirationDate = user.thinoWebExpir
            let newExpirationDate
            if (!oldExpirationDate) {
                newExpirationDate = new Date();
                newExpirationDate.setMonth(newExpirationDate.getMonth() + 1 * goodsCount);
            } else {
                newExpirationDate = new Date(oldExpirationDate);
                newExpirationDate.setMonth(new Date(oldExpirationDate).getMonth() + 1 * goodsCount);
            }

        } else if (goodsName === 'ThinoWebYearlySuscribe') {
            const oldExpirationDate = user.thinoWebExpir
            let newExpirationDate
            if (!oldExpirationDate) {
                newExpirationDate = new Date();
                newExpirationDate.setFullYear(new Date().getFullYear() + 1 * goodsCount);
            } else {
                newExpirationDate = new Date(oldExpirationDate);
                newExpirationDate.setFullYear(new Date(oldExpirationDate).getFullYear() + 1 * goodsCount);
            }

        } else {
            throw Error('Suscribe订单类型错误')
        }

    }

    private async handleSupport(orderId: string, userId: string) {
        try {
            await this.usersService.addSupportUser(userId);
            await this.changeOrderStatus(orderId, 'paid');
        } catch (e) {
            throw Error('处理Support订单失败:' + e)
        }
    }

    private async handleCrowdfunding(orderId: string) {
        try {
            await this.changeOrderStatus(orderId, 'paid');


        } catch (e) {
            throw Error('处理众筹订单失败:' + e)

        }
    }

    async handlePaymentSuccess(orderId: string, totalAmount: number) {
        //从orderId中获取订单信息，然后进行支付成功的处理
        const order = await this.ordersService.findOrder({ id: orderId });
        if (!order) {
            throw Error('订单不存在')
        }

        if (order.status === 'paid') {
            throw Error('已支付')
        }

        const userId = order.creatorId;
        const goodsId = order.goodsId;
        const goodsCount = order.goodsCount;

        const goods = await this.goodsService.findOne(goodsId);
        if (!goods) {
            throw Error('商品不存在')
        }

        if (goods.price * goodsCount != totalAmount) {
            throw Error('订单金额不正确')
        }

        if (goods.id === '652cdg3w-sf33-4452-a120-b9b7135165df') {
            try {
                await this.usersService.addThinoUser(userId);
            } catch (e) {
                Logger.error('处理thino众筹订单失败:' + e)
            }
        }


        const goodsType = goods.type
        const goodsName = goods.name

        if (goodsType === 'suscribe') {
            await this.handleSuscribe(orderId, goodsName, userId, goodsCount)
        } else if (goodsType === 'support') {
            await this.handleSupport(orderId, userId)
        } else if (goodsType === 'crowdfunding') {
            await this.handleCrowdfunding(orderId)
            try {
                await this.verifyProductService.makeProductPurchased(userId, goodsId)
            } catch (e) {
                Logger.error('处理verfiy订单失败:' + e)
            }
        } else if (goodsType === 'other') {
            throw Error('商品类型未定义')
        } else {
            throw Error('商品类型错误')
        }
    }

    async handlePaymentPending(orderId: string) {
        await this.ordersService.updateOrderStatus(orderId, 'pending');
    }

    async handlePaymentClosed(orderId: string) {
        await this.ordersService.updateOrderStatus(orderId, 'canceled');
    }
}
