import { MigrationInterface, QueryRunner } from "typeorm";

export class createActivationRecordTable1709373013789 implements MigrationInterface {
    name = 'createActivationRecordTable1709373013789'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`activation_record\` (\`id\` varchar(36) NOT NULL, \`creatorId\` varchar(255) NOT NULL, \`type\` enum ('thino', 'templify', 'media-extend') NOT NULL, \`appid\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), INDEX \`IDX_94612a1f4e758185c884cb3662\` (\`creatorId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_94612a1f4e758185c884cb3662\` ON \`activation_record\``);
        await queryRunner.query(`DROP TABLE \`activation_record\``);
    }

}
