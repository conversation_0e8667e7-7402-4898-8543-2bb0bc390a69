import { MigrationInterface, QueryRunner } from "typeorm";

export class addUserType1684806160731 implements MigrationInterface {
    name = 'addUserType1684806160731'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`type\` enum ('user', 'insider') NOT NULL DEFAULT 'user'`);
        await queryRunner.query(`ALTER TABLE \`goods\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`goods\` ADD \`type\` enum ('suscribe', 'support', 'article', 'crowdfunding', 'other') NOT NULL DEFAULT 'other'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`goods\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`goods\` ADD \`type\` varchar(255) NOT NULL DEFAULT 'other'`);
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`type\` varchar(255) NOT NULL DEFAULT 'user'`);
    }

}
