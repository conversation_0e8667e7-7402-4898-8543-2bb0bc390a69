import { registerAs } from '@nestjs/config';

export default registerAs('wework', () => ({
    corpId: process.env.WEWORK_CORP_ID,
    corpSecret: process.env.WEWORK_CORP_SECRET,
    customerServiceId: process.env.CUSTOMER_SERVICE_ID,
    customerServiceSecret: process.env.CUSTOMER_SERVICE_SECRET,
    weworkApiSecret: process.env.WEWORK_API_SECRET,
    thinoHelperToken: process.env.THINO_HELPER_TOKEN
}));
