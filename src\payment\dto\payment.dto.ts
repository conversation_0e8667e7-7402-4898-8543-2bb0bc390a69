import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, Validate } from "class-validator";
import { IsExist } from "src/utils/validators/is-exists.validator";

export class PaymentDto {
    // @ApiProperty({ example: '8e4b02e6-5932-4c66-bb9c-8437d4780777' })
    // @Validate(IsExist, ['User', 'id'], { message: 'userNotExists' })
    // @IsNotEmpty()
    // creatorId: string;

    @ApiProperty({ example: '5e28ac68-43c4-4f9b-b8d0-e0a2ca033e0a' })
    @Validate(IsExist, ['Order', 'id'], { message: 'orderNotExists' })
    @IsNotEmpty()
    orderId: string;

    @ApiProperty({ example: '' })
    @IsOptional()
    returnURL?: string
    // @ApiProperty({ example: 'order description' })
    // description?: string;

    // @ApiProperty({ example: '1511d94f-278d-4cc9-b68a-b1497c8fcbb7' })
    // @Validate(IsExist, ['Goods', 'id'], { message: 'goodsNotExists' })
    // @IsNotEmpty()
    // goodsId: string;

    // @ApiProperty({ example: 1 })
    // @IsNotEmpty()
    // goodsCount: number;
}