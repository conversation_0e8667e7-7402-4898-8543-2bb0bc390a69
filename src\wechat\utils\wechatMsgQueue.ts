import { WechatMessage } from '../interfaces/wechat.interface';
// 生产者消费者队列
export default class WeChatMsgQueue {
    private queue: WechatMessage[] = [];
    private msgidList: number[] = [];
    private maxSize: number;

    constructor(maxSize: number) {
        this.maxSize = maxSize;
    }

    // 压入
    public enqueue(item: WechatMessage): void {
        if (this.queue.length === this.maxSize) {
            throw new Error('Queue is full');
        }
        // 使用 MsgId 去重复
        if (item.MsgId != undefined && !this.msgidList.includes(item.MsgId)) {
            this.queue.push(item);
            this.msgidList.push(item.MsgId);
        } else if (item.MsgId == undefined) {
            // TODO: 事件排重
            this.queue.push(item);
        }
    }

    // 弹出一个
    public dequeue(): WechatMessage | undefined {
        let item = this.queue.shift();
        if (item.MsgId != undefined) {
            this.msgidList.splice(this.msgidList.indexOf(item.MsgId), 1)
        }
        return item;
    }

    public isEmpty(): boolean {
        return this.queue.length === 0;
    }
}