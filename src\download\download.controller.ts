import { Controller, Get, HttpCode, HttpStatus, Param, Query, UseGuards, Request, DefaultValuePipe, ParseIntPipe, ParseBoolPipe, Post, Body } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags, ApiQuery, ApiResponse, ApiBody } from '@nestjs/swagger';
import { ObsidianPluginService } from './services/obsidian_plugin.service';
import { AuthGuard } from '@nestjs/passport';
import { Roles } from 'src/roles/roles.decorator';
import { RoleEnum } from 'src/roles/roles.enum';
import { RolesGuard } from 'src/roles/roles.guard';
import { ObsidianThemeService } from './services/obsidian_theme.service';
import { ZoteroPluginService } from './services/zotero_plugin.service';
import { DownloadService } from './services/download.service';

@ApiTags('Download')
@Controller({ path: 'download', version: '1' })
export class DownloadController {
    constructor(
        private readonly downloadService: DownloadService,
        private readonly obsidianPluginService: ObsidianPluginService,
        private readonly obsidianThemeService: ObsidianThemeService,
        private readonly zoteroPluginService: ZoteroPluginService
    ) { }

    @ApiOperation({ summary: '更新所有Obsidian插件', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'), RolesGuard)
    @Roles(RoleEnum.admin)
    @Get('obsidian/updateAllObsidianPlugins')
    @HttpCode(HttpStatus.OK)
    async updateAllObsidianPlugins() {
        return await this.obsidianPluginService.updateObsidianPlugins();
    }

    @ApiOperation({ summary: '更新所有Obsidian插件', description: '限制为200次每天' })
    @Get('obsidian/updateAllObsidianPlugins2')
    @HttpCode(HttpStatus.OK)
    async updateAllObsidianPlugins2() {
        return await this.obsidianPluginService.updateObsidianPlugins();
    }


    @ApiOperation({ summary: '用户获取Obsidian插件剩余下载次数', description: '限制为200次每天' })
    @Get('obsidian/getPluginDownloadCount')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getDownloadCount(@Request() request) {
        const user = request.user;
        if (!user) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            };
        }
        return await this.downloadService.getLeftDownloadCount(user.id);
    }
    
    @ApiOperation({ summary: '通过Obsidian插件id和版本号获取下载链接的CDN鉴权链接', description: '限制为200次每天' })
    @Get('obsidian/getPluginDownloadUrl/:pluginId/:version')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getPluginDownloadUrl(
        @Request() request,
        @Param('pluginId') pluginId: string,
        @Param('version') version: string,
        @Query('useVersion') useVersion?: string // 新增可选查询参数，值为 'true' 时使用传入的版本号
    ) {
        const userID = request.user.id;
        if (!userID) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            };
        }
        // 将 useVersion 转换为 boolean 类型，值为 'true' 时为 true，否则为 false
        const shouldUseVersion = useVersion === 'true';
        return await this.obsidianPluginService.generateSignedUrl(userID, pluginId, version, shouldUseVersion);
    }
    
    @ApiOperation({ summary: '获取Obsidian下载量前20个插件列表',description:'无限制' })
    @Get('obsidian/getTop20Plugins')
    @HttpCode(HttpStatus.OK)
    async getTop20Plugins() {
        return await this.obsidianPluginService.getTop20Plugins();
    }

    @ApiOperation({ summary: '获取全部Obsidian插件，限制为200次每天', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/getAllPlugins')
    @HttpCode(HttpStatus.OK)
    async getAllPlugins(@Request() request) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.obsidianPluginService.getAllPlugins(userId);
    }

    @ApiOperation({ 
      summary: '分页获取Obsidian插件，限制为200次每天', 
      description: '限制为200次每天，返回带有分页信息的插件列表'
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/getPluginsPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为downloadCount，可选值：downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ 
      status: 200, 
      description: '成功返回插件分页数据',
      schema: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            description: '插件列表',
            items: {
              type: 'object'
            }
          },
          meta: {
            type: 'object',
            description: '分页元数据',
            properties: {
              page: { type: 'number', description: '当前页码' },
              limit: { type: 'number', description: '每页数量' },
              total: { type: 'number', description: '总数据量' },
              totalPages: { type: 'number', description: '总页数' },
              sortBy: { type: 'string', description: '排序字段' },
              sortOrder: { type: 'string', description: '排序方向' }
            }
          }
        }
      }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getPluginsPaginated(
        @Request() request,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('downloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.obsidianPluginService.getPluginsPaginated(userId, page, limit, sortBy, sortOrder);
    }

    @ApiOperation({ 
      summary: '搜索Obsidian插件并分页，限制为200次每天', 
      description: '限制为200次每天，支持对插件ID、名称、作者、描述、中文描述和标签进行搜索' 
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/searchPluginsPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'searchText', description: '搜索关键词，支持模糊搜索ID、名称、作者、描述等', required: false, type: String })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为downloadCount，可选值：downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ 
      status: 200, 
      description: '成功返回搜索结果的分页数据',
      schema: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            description: '搜索结果列表',
            items: {
              type: 'object'
            }
          },
          meta: {
            type: 'object',
            description: '分页元数据',
            properties: {
              page: { type: 'number', description: '当前页码' },
              limit: { type: 'number', description: '每页数量' },
              total: { type: 'number', description: '总数据量' },
              totalPages: { type: 'number', description: '总页数' },
              sortBy: { type: 'string', description: '排序字段' },
              sortOrder: { type: 'string', description: '排序方向' }
            }
          }
        }
      }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async searchPluginsPaginated(
        @Request() request,
        @Query('searchText') searchText: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('downloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.obsidianPluginService.searchPluginsPaginated(userId, searchText || '', page, limit, sortBy, sortOrder);
    }

    @ApiOperation({ summary: '获取Obsidian插件的所有标签分类', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/getPluginTags')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({ 
        status: 200, 
        description: '成功返回标签列表及其统计数量',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    tag: { type: 'string', description: '标签名称' },
                    count: { type: 'number', description: '标签出现次数' }
                }
            }
        }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getPluginTags(@Request() request) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.obsidianPluginService.getPluginTags(userId);
    }

    @ApiOperation({ summary: '根据标签获取Obsidian插件并分页', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/getPluginsByTag')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'tag', description: '标签名称', required: true, type: String })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为downloadCount，可选值：downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiQuery({ name: 'fuzzyMatch', description: '是否启用模糊匹配，默认为false(精确匹配)，设为true则进行模糊搜索', required: false, type: Boolean })
    @ApiResponse({ 
      status: 200, 
      description: '成功返回标签筛选结果的分页数据',
      schema: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            description: '筛选结果列表',
            items: {
              type: 'object'
            }
          },
          meta: {
            type: 'object',
            description: '分页元数据',
            properties: {
              page: { type: 'number', description: '当前页码' },
              limit: { type: 'number', description: '每页数量' },
              total: { type: 'number', description: '总数据量' },
              totalPages: { type: 'number', description: '总页数' },
              sortBy: { type: 'string', description: '排序字段' },
              sortOrder: { type: 'string', description: '排序方向' },
              tag: { type: 'string', description: '筛选标签' },
              fuzzyMatch: { type: 'boolean', description: '是否启用了模糊匹配' }
            }
          }
        }
      }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getPluginsByTag(
        @Request() request,
        @Query('tag') tag: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('downloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC',
        @Query('fuzzyMatch', new DefaultValuePipe(false), ParseBoolPipe) fuzzyMatch: boolean
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        if (!tag) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '请提供标签名称',
            }
        }
        return await this.obsidianPluginService.getPluginsByTag(userId, tag, page, limit, sortBy, sortOrder, fuzzyMatch);
    }

    
    @ApiOperation({ summary: '根据来源获取Obsidian插件并分页', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/getPluginsBySource')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'source', description: '插件来源，可选值：official,community', required: true, enum: ['official', 'community'] })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为downloadCount，可选值：downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ 
        status: 200, 
        description: '成功返回来源筛选结果的分页数据',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    description: '筛选结果列表',
                    items: {
                        type: 'object'
                    }
                },
                meta: {
                    type: 'object',
                    description: '分页元数据',
                    properties: {
                        page: { type: 'number', description: '当前页码' },
                        limit: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数据量' },
                        totalPages: { type: 'number', description: '总页数' },
                        sortBy: { type: 'string', description: '排序字段' },
                        sortOrder: { type: 'string', description: '排序方向' },
                        source: { type: 'string', description: '插件来源' }
                    }
                }
            }
        }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getPluginsBySource(
        @Request() request,
        @Query('source') source: 'official' | 'community',
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('downloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        if (!source || !['official', 'community'].includes(source)) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '请提供有效的插件来源(official或community)',
            }
        }
        return await this.obsidianPluginService.getPluginsBySource(userId, source, page, limit, sortBy, sortOrder);
    }

    @ApiOperation({ summary: '更新所有Obsidian主题', description: '仅管理员可操作' })
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('jwt'), RolesGuard)
    // @Roles(RoleEnum.admin)
    @Get('obsidian/updateAllObsidianThemes')
    @HttpCode(HttpStatus.OK)
    async updateAllObsdianThemes() {
        return await this.obsidianThemeService.updateObsidianThemes();
    }

    @ApiOperation({ summary: '获取Obsidian下载量前20个主题列表', description: '无限制' })
    @Get('obsidian/getTop20Themes')
    @HttpCode(HttpStatus.OK)
    async getTop20Themes() {
        return await this.obsidianThemeService.getTop20ObsidianThemes();
    }

    @ApiOperation({ summary: '获取全部Obsidian主题，限制为200次每天', description: '限制为200次每天' })
    @Get('obsidian/getAllThemes')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async findAllThemes(@Request() request) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }

        return await this.obsidianThemeService.getAllObsidianThemes(userId);
    }

    @ApiOperation({ summary: '根据插件ID获取所有版本的下载地址', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get(':pluginId/versions')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为100', required: false, type: Number })
    @ApiResponse({
        status: 200,
        description: '成功返回插件所有版本的下载地址分页数据',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    description: '插件版本下载地址列表',
                    items: {
                        type: 'string',
                        description: '下载地址',
                        example: 'https://download.pkmer.cn/obsidian-plugins/2hop-links-plus-0.34.3.zip',
                    },
                },
                meta: {
                    type: 'object',
                    description: '分页元数据',
                    properties: {
                        page: { type: 'number', description: '当前页码' },
                        limit: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数据量' },
                        totalPages: { type: 'number', description: '总页数' },
                    },
                },
            },
        },
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    @ApiResponse({ status: 404, description: '插件ID无效或未找到相关版本' })
    async getPluginVersions(
        @Request() request,
        @Param('pluginId') pluginId: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            };
        }
        if (!pluginId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '请提供有效的插件ID',
            };
        }
        // 调用服务层获取插件版本列表
        const versions = await this.downloadService.getPluginVersions(pluginId);
        if (!versions || versions.length === 0) {
            return {
                code: HttpStatus.NOT_FOUND,
                message: `未找到插件 ${pluginId} 的任何版本`,
            };
        }
        // 处理分页逻辑
        const total = versions.length;
        const totalPages = Math.ceil(total / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedVersions = versions.slice(startIndex, endIndex);
        return {
            data: paginatedVersions,
            meta: {
                page,
                limit,
                total,
                totalPages,
            },
        };
    }



    @ApiOperation({ summary: '分页获取Obsidian主题，限制为200次每天', description: '限制为200次每天' })
    @Get('obsidian/getThemesPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为pkmerDownloadCount，可选值：pkmerDownloadCount,id,name,author,version,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ status: 200, description: '成功返回主题分页数据' })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getThemesPaginated(
        @Request() request,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('pkmerDownloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.obsidianThemeService.getThemesPaginated(userId, page, limit, sortBy, sortOrder);
    }

    @ApiOperation({ summary: '搜索Obsidian主题并分页，限制为200次每天', description: '限制为200次每天' })
    @Get('obsidian/searchThemesPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiQuery({ name: 'searchText', description: '搜索关键词，支持模糊搜索ID、名称、作者、描述等', required: false, type: String })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为pkmerDownloadCount，可选值：pkmerDownloadCount,id,name,author,version,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ status: 200, description: '成功返回搜索结果的分页数据' })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async searchThemesPaginated(
        @Request() request,
        @Query('searchText') searchText: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('pkmerDownloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.obsidianThemeService.searchThemesPaginated(userId, searchText || '', page, limit, sortBy, sortOrder);
    }

    @ApiOperation({ summary: '根据主题名和版本号，获取鉴权下载链接', description: '限制为200次每天' })
    @Get('obsidian/getThemeDownloadUrl/:themeName/:version')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getThemeDownloadUrl(
        @Request() request, 
        @Param('themeName') themeName: string, 
        @Param('version') version: string
    ) {
        const userID = request.user.id;
        if (!userID) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            };
        }

        return await this.obsidianThemeService.generateSignedObsidianThemeDownloadUrl(userID, themeName,version);

    }

   

    @ApiOperation({ summary: '获取Obsidian主题的所有标签分类', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/getThemeTags')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({ 
        status: 200, 
        description: '成功返回标签列表及其统计数量',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    tag: { type: 'string', description: '标签名称' },
                    count: { type: 'number', description: '标签出现次数' }
                }
            }
        }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getThemeTags(@Request() request) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.obsidianThemeService.getThemeTags(userId);
    }

    @ApiOperation({ summary: '根据标签获取Obsidian主题并分页', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('obsidian/getThemesByTag')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'tag', description: '标签名称', required: true, type: String })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为pkmerDownloadCount，可选值：pkmerDownloadCount,id,name,author,version,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiQuery({ name: 'fuzzyMatch', description: '是否启用模糊匹配，默认为false(精确匹配)，设为true则进行模糊搜索', required: false, type: Boolean })
    @ApiResponse({ 
        status: 200, 
        description: '成功返回标签筛选结果的分页数据',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    description: '筛选结果列表'
                },
                meta: {
                    type: 'object',
                    description: '分页元数据',
                    properties: {
                        page: { type: 'number', description: '当前页码' },
                        limit: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数据量' },
                        totalPages: { type: 'number', description: '总页数' },
                        sortBy: { type: 'string', description: '排序字段' },
                        sortOrder: { type: 'string', description: '排序方向' },
                        tag: { type: 'string', description: '筛选标签' },
                        fuzzyMatch: { type: 'boolean', description: '是否启用了模糊匹配' }
                    }
                }
            }
        }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getThemesByTag(
        @Request() request,
        @Query('tag') tag: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('pkmerDownloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC',
        @Query('fuzzyMatch', new DefaultValuePipe(false), ParseBoolPipe) fuzzyMatch: boolean
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        if (!tag) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '请提供标签名称',
            }
        }
        return await this.obsidianThemeService.getThemesByTag(userId, tag, page, limit, sortBy, sortOrder, fuzzyMatch);
    }


    @ApiOperation({ summary: '根据主题名称获取所有版本的下载地址', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get(':themeName/themeversions')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为100', required: false, type: Number })
    @ApiResponse({
        status: 200,
        description: '成功返回主题所有版本的下载地址分页数据',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    description: '主题版本下载地址列表',
                    items: {
                        type: 'string',
                        description: '下载地址',
                        example: 'https://download.pkmer.cn/obsidian-themes/Blue Topaz-2024041102.zip',
                    },
                },
                meta: {
                    type: 'object',
                    description: '分页元数据',
                    properties: {
                        page: { type: 'number', description: '当前页码' },
                        limit: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数据量' },
                        totalPages: { type: 'number', description: '总页数' },
                    },
                },
            },
        },
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    @ApiResponse({ status: 404, description: '插件ID无效或未找到相关版本' })
    async getthemeVersions(
        @Request() request,
        @Param('themeName') themeName: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(100), ParseIntPipe) limit: number,
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            };
        }
        if (!themeName) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '请提供有效的插件ID',
            };
        }
        // 调用服务层获取插件版本列表
        const versions = await this.downloadService.getThemeVersions(themeName);
        if (!versions || versions.length === 0) {
            return {
                code: HttpStatus.NOT_FOUND,
                message: `未找到主题 ${themeName} 的任何版本`,
            };
        }
        // 处理分页逻辑
        const total = versions.length;
        const totalPages = Math.ceil(total / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedVersions = versions.slice(startIndex, endIndex);
        return {
            data: paginatedVersions,
            meta: {
                page,
                limit,
                total,
                totalPages,
            },
        };
    }




    @ApiOperation({ summary: '更新所有Zotero插件', description: '仅管理员可操作' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'), RolesGuard)
    @Roles(RoleEnum.admin)
    @Get('zotero/updateAllZoteroPlugins')
    @HttpCode(HttpStatus.OK)
    async updateAllZoteroPlugins() {
        return await this.zoteroPluginService.updateZoteroPlugins();
    }

    @ApiOperation({ summary: '获取Zotero下载量前20个插件列表', description: '无限制' })
    @Get('zotero/getTop20Plugins')
    @HttpCode(HttpStatus.OK)
    async getTop20ZoteroPlugins() {
        return await this.zoteroPluginService.getTop20Plugins();
    }

    @ApiOperation({ summary: '获取全部Zotero插件，限制为200次每天', description: '限制为200次每天' })
    @Get('zotero/getAllPlugins')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getAllZoteroPlugins(@Request() request) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.zoteroPluginService.getAllPlugins(userId);
    }

    @ApiOperation({ summary: '分页获取Zotero插件，限制为200次每天', description: '限制为200次每天' })
    @Get('zotero/getPluginsPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为pkmerDownloadCount，可选值：pkmerDownloadCount,downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ status: 200, description: '成功返回插件分页数据' })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 404, description: '用户未找到' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getZoteroPluginsPaginated(
        @Request() request,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('pkmerDownloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.zoteroPluginService.getPluginsPaginated(userId, page, limit, sortBy, sortOrder);
    }

    @ApiOperation({ summary: '搜索Zotero插件并分页，限制为200次每天', description: '限制为200次每天' })
    @Get('zotero/searchPluginsPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiQuery({ name: 'searchText', description: '搜索关键词，支持模糊搜索ID、名称、作者、描述、标签等', required: false, type: String })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为pkmerDownloadCount，可选值：pkmerDownloadCount,downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ status: 200, description: '成功返回搜索结果的分页数据' })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 404, description: '用户未找到' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async searchZoteroPluginsPaginated(
        @Request() request,
        @Query('searchText') searchText: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('pkmerDownloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.zoteroPluginService.searchPluginsPaginated(userId, searchText || '', page, limit, sortBy, sortOrder);
    }

    @ApiOperation({ summary: '获取Zotero插件名和版本号，获取鉴权下载链接', description: '限制为200次每天' })
    @Get('zotero/getPluginDownloadUrl/:pluginName/:version')
    @HttpCode(HttpStatus.OK)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getZoteroPluginDownloadUrl(@Request() request, @Param('pluginName') pluginName: string, @Param('version') version: string) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }

        const url = 'https://download.pkmer.cn/zotero-plugins/' + pluginName + '-' + version + '.xpi'
        return await this.zoteroPluginService.generateSignedZoteroPluginDownloadUrl(userId, url);
    }

    @ApiOperation({ summary: '获取Zotero插件的所有标签分类', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('zotero/getPluginTags')
    @HttpCode(HttpStatus.OK)
    @ApiResponse({ 
        status: 200, 
        description: '成功返回标签列表及其统计数量',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    tag: { type: 'string', description: '标签名称' },
                    count: { type: 'number', description: '标签出现次数' }
                }
            }
        }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 404, description: '用户未找到' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getZoteroPluginTags(@Request() request) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        return await this.zoteroPluginService.getPluginTags(userId);
    }

    @ApiOperation({ summary: '根据标签获取Zotero插件并分页', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Get('zotero/getPluginsByTag')
    @HttpCode(HttpStatus.OK)
    @ApiQuery({ name: 'tag', description: '标签名称', required: true, type: String })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'limit', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为pkmerDownloadCount，可选值：pkmerDownloadCount,downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiQuery({ name: 'fuzzyMatch', description: '是否启用模糊匹配，默认为false(精确匹配)，设为true则进行模糊搜索', required: false, type: Boolean })
    @ApiResponse({ 
        status: 200, 
        description: '成功返回标签筛选结果的分页数据',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    description: '筛选结果列表'
                },
                meta: {
                    type: 'object',
                    description: '分页元数据',
                    properties: {
                        page: { type: 'number', description: '当前页码' },
                        limit: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数据量' },
                        totalPages: { type: 'number', description: '总页数' },
                        sortBy: { type: 'string', description: '排序字段' },
                        sortOrder: { type: 'string', description: '排序方向' },
                        tag: { type: 'string', description: '筛选标签' },
                        fuzzyMatch: { type: 'boolean', description: '是否启用了模糊匹配' }
                    }
                }
            }
        }
    })
    @ApiResponse({ status: 403, description: 'API调用次数过多，请稍候重试' })
    @ApiResponse({ status: 404, description: '用户未找到' })
    @ApiResponse({ status: 400, description: '用户不存在或用户需重新登录' })
    async getZoteroPluginsByTag(
        @Request() request,
        @Query('tag') tag: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(24), ParseIntPipe) limit: number,
        @Query('sortBy', new DefaultValuePipe('pkmerDownloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC',
        @Query('fuzzyMatch', new DefaultValuePipe(false), ParseBoolPipe) fuzzyMatch: boolean
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        if (!tag) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '请提供标签名称',
            }
        }
        return await this.zoteroPluginService.getPluginsByTag(userId, tag, page, limit, sortBy, sortOrder, fuzzyMatch);
    }

    @ApiOperation({ summary: '获取已安装插件的详细信息（分页）', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('obsidian/getInstalledPluginsPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiBody({
        description: '已安装的插件ID列表',
        schema: {
            type: 'object',
            required: ['pluginIds'],
            properties: {
                pluginIds: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    description: '插件ID列表'
                }
            },
            example: {
                pluginIds: ['obsidian-admonition', 'obsidian-excalidraw', 'dataview']
            }
        }
    })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'pageSize', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为downloadCount，可选值：downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ 
        status: 200, 
        description: '成功返回已安装插件的分页数据',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    description: '插件列表'
                },
                meta: {
                    type: 'object',
                    description: '分页元数据',
                    properties: {
                        page: { type: 'number', description: '当前页码' },
                        pageSize: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数据量' },
                        totalPages: { type: 'number', description: '总页数' },
                        sortBy: { type: 'string', description: '排序字段' },
                        sortOrder: { type: 'string', description: '排序方向' }
                    }
                }
            }
        }
    })
    async getInstalledPluginsPaginated(
        @Request() request,
        @Body() body: any,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('pageSize', new DefaultValuePipe(24), ParseIntPipe) pageSize: number,
        @Query('sortBy', new DefaultValuePipe('downloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        
  
        
        // 读取原始请求（这可能在某些情况下需要中间件支持）
        let rawBody = '';
        if (request.readable) {
            const chunks = [];
            for await (const chunk of request) {
                chunks.push(chunk);
            }
            rawBody = Buffer.concat(chunks).toString();
       //     console.log('原始请求流:', rawBody);
        } else if (request.rawBody) {
            rawBody = request.rawBody;
         //   console.log('从request.rawBody获取原始请求体:', rawBody);
        }
        
        // 尝试从多种可能的格式中获取 pluginIds
        let pluginIds: string[] = null;
        
        // 先检查Content-Type是否为text/plain
        if (request.headers['content-type'] && request.headers['content-type'].includes('text/plain')) {
           // console.log('检测到text/plain内容类型');
            
            // 尝试从请求体中提取原始内容
            const requestBodyText = rawBody || (typeof request.body === 'string' ? request.body : '');
            
            if (requestBodyText) {
                try {
                //    console.log('尝试解析原始文本:', requestBodyText);
                    const parsedBody = JSON.parse(requestBodyText);
                    if (parsedBody.pluginIds && Array.isArray(parsedBody.pluginIds)) {
                        pluginIds = parsedBody.pluginIds;
          //              console.log('从text/plain请求体成功解析出pluginIds');
                    }
                } catch (e) {
                    console.error('解析text/plain请求体失败:', e);
                }
            }
        }
        
        // 如果上述特殊处理未成功，继续尝试其他方式
        if (!pluginIds) {
            // 1. 尝试直接从请求体中获取 pluginIds
            if (request.body && request.body.pluginIds && Array.isArray(request.body.pluginIds)) {
                pluginIds = request.body.pluginIds;
          //      console.log('从请求体的 request.body.pluginIds 获取到插件列表');
            }
            // 2. 尝试从 Body 装饰器解析的结果中获取
            else if (body && body.pluginIds && Array.isArray(body.pluginIds)) {
                pluginIds = body.pluginIds;
           //     console.log('从 Body 装饰器解析的 body.pluginIds 获取到插件列表');
            }
            // 3. 如果 body 本身就是数组
            else if (Array.isArray(body)) {
                pluginIds = body;
         //       console.log('从数组类型的 body 获取到插件列表');
            }
            // 4. 如果 request.body 本身就是数组
            else if (Array.isArray(request.body)) {
                pluginIds = request.body;
         //       console.log('从数组类型的 request.body 获取到插件列表');
            }
            // 5. 在Express的原始请求上，可能直接包含请求体
            else if (request.raw && request.raw.body) {
                try {
                    const rawBody = request.raw.body;
                    if (typeof rawBody === 'string') {
                        const parsed = JSON.parse(rawBody);
                        if (parsed.pluginIds && Array.isArray(parsed.pluginIds)) {
                            pluginIds = parsed.pluginIds;
                    //        console.log('从原始请求体字符串解析获取到插件列表');
                        } else if (Array.isArray(parsed)) {
                            pluginIds = parsed;
                    //        console.log('从原始请求体字符串解析得到的数组获取到插件列表');
                        }
                    } else if (typeof rawBody === 'object') {
                        if (rawBody.pluginIds && Array.isArray(rawBody.pluginIds)) {
                            pluginIds = rawBody.pluginIds;
                   //         console.log('从原始请求体对象获取到插件列表');
                        }
                    }
                } catch (e) {
                    console.error('解析原始请求体失败:', e);
                }
            }
        }
        
        // // 如果所有方法都失败了，尝试在查询参数中寻找
        // if (!pluginIds) {
        //     try {
        //         const url = new URL(request.url, `http://${request.headers.host || 'localhost'}`);
        //         const pluginIdsParam = url.searchParams.get('pluginIds');
        //         if (pluginIdsParam) {
        //             try {
        //                 pluginIds = JSON.parse(pluginIdsParam);
        //                 console.log('从URL查询参数解析获取到插件列表');
        //             } catch {
        //                 // 可能是逗号分隔的字符串
        //                 pluginIds = pluginIdsParam.split(',');
        //                 console.log('从URL查询参数(逗号分隔)获取到插件列表');
        //             }
        //         }
        //     } catch (e) {
        //         console.error('解析URL参数失败:', e);
        //     }
        // }

        // // 最后，尝试一种特殊情况：请求体可能被接收但格式不正确
        // if (!pluginIds && typeof request.body === 'string') {
        //     try {
        //         // 尝试解析为JSON
        //         const parsedBody = JSON.parse(request.body);
        //         if (parsedBody.pluginIds && Array.isArray(parsedBody.pluginIds)) {
        //             pluginIds = parsedBody.pluginIds;
        //             console.log('从字符串请求体解析获取到插件列表');
        //         }
        //     } catch (e) {
        //         console.error('解析字符串请求体失败:', e);
        //     }
        // }
        
        // console.log('最终解析的 pluginIds:', pluginIds);
        
        // // 万一所有方法都失败，直接尝试解析原始请求信息
        // if (!pluginIds || !Array.isArray(pluginIds) || pluginIds.length === 0) {
        //     console.error('无法获取有效的 pluginIds 列表。请求详情:', {
        //         method: request.method,
        //         url: request.url,
        //         headers: request.headers,
        //         queryParams: request.query,
        //         body: body,
        //         rawBody: request.body
        //     });
            
        //     // 到这里还未获取到，表明需要使用中间件处理
        //     return {
        //         code: HttpStatus.BAD_REQUEST,
        //         message: '请提供有效的插件ID列表',
        //         debug: {
        //             receivedBody: body,
        //             contentType: request.headers['content-type'],
        //             contentLength: request.headers['content-length'],
        //             note: '前端请使用application/json作为Content-Type'
        //         }
        //     }
        // }
        
        return await this.obsidianPluginService.getInstalledPluginsPaginated(
            userId,
            pluginIds,
            page,
            pageSize,
            sortBy,
            sortOrder
        );
    }

    @ApiOperation({ summary: '获取需要更新的插件列表（分页）', description: '限制为200次每天' })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('obsidian/getOutdatedPluginsPaginated')
    @HttpCode(HttpStatus.OK)
    @ApiBody({
        description: '需要检查更新的插件列表（包含ID和当前版本号）',
        schema: {
            type: 'object',
            required: ['plugins'],
            properties: {
                plugins: {
                    type: 'array',
                    items: {
                        type: 'object',
                        required: ['id', 'version'],
                        properties: {
                            id: {
                                type: 'string',
                                description: '插件ID'
                            },
                            version: {
                                type: 'string',
                                description: '当前安装的版本号'
                            }
                        }
                    },
                    description: '插件ID和版本列表'
                }
            },
            example: {
                plugins: [
                    { id: 'obsidian-admonition', version: '9.0.5' },
                    { id: 'obsidian-excalidraw', version: '1.8.20' }
                ]
            }
        }
    })
    @ApiQuery({ name: 'page', description: '页码，默认为1', required: false, type: Number })
    @ApiQuery({ name: 'pageSize', description: '每页数量，默认为24', required: false, type: Number })
    @ApiQuery({ name: 'sortBy', description: '排序字段，默认为downloadCount，可选值：downloadCount,id,name,author,version,pluginUpdatedTime,updatedTime', required: false, type: String })
    @ApiQuery({ name: 'sortOrder', description: '排序方向，默认为DESC，可选值：ASC,DESC', required: false, enum: ['ASC', 'DESC'] })
    @ApiResponse({ 
        status: 200, 
        description: '成功返回需要更新的插件分页数据',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    description: '需要更新的插件列表'
                },
                meta: {
                    type: 'object',
                    description: '分页元数据',
                    properties: {
                        page: { type: 'number', description: '当前页码' },
                        pageSize: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数据量' },
                        totalPages: { type: 'number', description: '总页数' },
                        sortBy: { type: 'string', description: '排序字段' },
                        sortOrder: { type: 'string', description: '排序方向' }
                    }
                }
            }
        }
    })
    async getOutdatedPluginsPaginated(
        @Request() request,
        @Body() body: any,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('pageSize', new DefaultValuePipe(24), ParseIntPipe) pageSize: number,
        @Query('sortBy', new DefaultValuePipe('downloadCount')) sortBy: string,
        @Query('sortOrder', new DefaultValuePipe('DESC')) sortOrder: 'ASC' | 'DESC'
    ) {
        const userId = request.user.id;
        if (!userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            }
        }
        
      
        
        // 读取原始请求（这可能在某些情况下需要中间件支持）
        let rawBody = '';
        if (request.readable) {
            const chunks = [];
            for await (const chunk of request) {
                chunks.push(chunk);
            }
            rawBody = Buffer.concat(chunks).toString();
        //    console.log('原始请求流:', rawBody);
        } else if (request.rawBody) {
            rawBody = request.rawBody;
       //     console.log('从request.rawBody获取原始请求体:', rawBody);
        }
        
        // 尝试从多种可能的格式中获取 plugins
        let plugins: { id: string, version: string }[] = null;
        
        // 先检查Content-Type是否为text/plain
        if (request.headers['content-type'] && request.headers['content-type'].includes('text/plain')) {
       //     console.log('检测到text/plain内容类型');
            
            // 尝试从请求体中提取原始内容
            const requestBodyText = rawBody || (typeof request.body === 'string' ? request.body : '');
            
            if (requestBodyText) {
                try {
            //       console.log('尝试解析原始文本:', requestBodyText);
                    const parsedBody = JSON.parse(requestBodyText);
                    if (parsedBody.plugins && Array.isArray(parsedBody.plugins)) {
                        plugins = parsedBody.plugins;
              //          console.log('从text/plain请求体成功解析出plugins');
                    }
                } catch (e) {
                    console.error('解析text/plain请求体失败:', e);
                }
            }
        }
        
        // 如果特殊处理未成功，继续尝试其他方式
        if (!plugins) {
            // 1. 尝试从请求体中获取 plugins
            if (request.body && request.body.plugins && Array.isArray(request.body.plugins)) {
                plugins = request.body.plugins;
          //      console.log('从请求体的 request.body.plugins 获取到插件列表');
            }
            // 2. 尝试从 Body 装饰器解析的结果中获取
            else if (body && body.plugins && Array.isArray(body.plugins)) {
                plugins = body.plugins;
           //     console.log('从 Body 装饰器解析的 body.plugins 获取到插件列表');
            }
            // 3. 如果 body 本身就是数组
            else if (Array.isArray(body)) {
                plugins = body;
           //     console.log('从数组类型的 body 获取到插件列表');
            }
            // 4. 如果 request.body 本身就是数组
            else if (Array.isArray(request.body)) {
                plugins = request.body;
           //     console.log('从数组类型的 request.body 获取到插件列表');
            }
            // 5. 在Express的原始请求上，可能直接包含请求体
            else if (request.raw && request.raw.body) {
                try {
                    const rawBody = request.raw.body;
                    if (typeof rawBody === 'string') {
                        const parsed = JSON.parse(rawBody);
                        if (parsed.plugins && Array.isArray(parsed.plugins)) {
                            plugins = parsed.plugins;
              //              console.log('从原始请求体字符串解析获取到插件列表');
                        } else if (Array.isArray(parsed)) {
                            plugins = parsed;
                  //          console.log('从原始请求体字符串解析得到的数组获取到插件列表');
                        }
                    } else if (typeof rawBody === 'object') {
                        if (rawBody.plugins && Array.isArray(rawBody.plugins)) {
                            plugins = rawBody.plugins;
                   //         console.log('从原始请求体对象获取到插件列表');
                        }
                    }
                } catch (e) {
                    console.error('解析原始请求体失败:', e);
                }
            }
        }
        
         
        return await this.obsidianPluginService.getOutdatedPluginsPaginated(
            userId,
            plugins,
            page,
            pageSize,
            sortBy,
            sortOrder
        );
    }

}

