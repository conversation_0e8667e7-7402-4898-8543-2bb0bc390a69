import { PartialType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';

import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../../roles/entities/role.entity';
import { IsEmail, IsOptional, IsPhoneNumber, MinLength, Validate } from 'class-validator';
import { Status } from '../../statuses/entities/status.entity';
import { IsNotExist } from '../../utils/validators/is-not-exists.validator';
import { FileEntity } from '../../files/entities/file.entity';
import { IsExist } from '../../utils/validators/is-exists.validator';

export class UpdateUserDto extends PartialType(CreateUserDto) {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value?.toLowerCase().trim())
    @IsOptional()
    @Validate(IsNotExist, ['User'], {
        message: 'emailAlreadyExists',
    })
    @IsEmail()
    email?: string | null;

    @ApiProperty({ example: '13455789531' })
    @Transform(({ value }) => value?.toLowerCase().trim())
    @IsOptional()
    @IsPhoneNumber('CN')
    phone?: string | null

    @ApiProperty()
    @IsOptional()
    @MinLength(6)
    password?: string;

    provider?: string;

    @ApiProperty()
    @IsOptional()
    openid?: string;

    @ApiProperty()
    @IsOptional()
    subscribeWechat?: boolean;

    socialId?: string | null;

    @ApiProperty({ example: 'John' })
    @IsOptional()
    nickName?: string | null;

    @ApiProperty({ type: () => FileEntity })
    @IsOptional()
    avatar?: string | null;

    @ApiProperty({ type: Role })
    @IsOptional()
    @Validate(IsExist, ['Role', 'id'], {
        message: 'roleNotExists',
    })
    role?: Role | null;

    @ApiProperty({ example: '{}' })
    @IsOptional()
    settings?: string;

    @ApiProperty({ example: '{}' })
    @IsOptional()
    article?: string;

    @ApiProperty({ type: Status })
    @IsOptional()
    @Validate(IsExist, ['Status', 'id'], {
        message: 'statusNotExists',
    })
    status?: Status;

    @ApiProperty()
    @IsOptional()
    thinoOpenId?: string | null;
}
