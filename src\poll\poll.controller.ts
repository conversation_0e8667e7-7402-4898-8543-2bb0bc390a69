import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Query, UseGuards, Request, UnauthorizedException, Req } from '@nestjs/common';
import { PollService } from './poll.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { CreatePollRecordDto } from './dto/create-poll-record.dto';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from 'src/roles/roles.guard';
import { Roles } from 'src/roles/roles.decorator';
import { RoleEnum } from 'src/roles/roles.enum';

@ApiTags('Poll')
@Controller({ path: 'poll', version: '1' })
export class PollController {
    constructor(private readonly pollService: PollService) { }

    @ApiOperation({ summary: '获取所有投票项目',description: '无限制' })
    @Get('getAllPolls')
    @HttpCode(HttpStatus.OK)
    async getAllPolls() {
        return this.pollService.getAllPolls();
    }

    @ApiOperation({ summary: '根据投票项目ID获取指定投票项目', description: '无限制' })
    @Get('getPollItem/:pollId')
    @HttpCode(HttpStatus.OK)
    async getPollById(@Param('pollId') pollId: string) {
        return this.pollService.getPollById(pollId);
    }

    @ApiBearerAuth()
    @ApiOperation({ summary: '添加新的投票记录', description: '用户权限' })
    @Roles(RoleEnum.user)
    @UseGuards(AuthGuard('jwt'), RolesGuard)
    @Post('addNewPollRecord')
    @HttpCode(HttpStatus.CREATED)
    async addNewPollRecord(@Body() createPollRecordDto: CreatePollRecordDto, @Request() request) {
        return this.pollService.addNewPollRecord(request.user.id, createPollRecordDto);
    }

    @ApiOperation({ summary: '通过投票项目ID获取投票结果', description: '无限制' })
    @Get('getPollResultByPollId/:pollId')
    async getPollResultByPollId(@Param('pollId') pollId: string) {
        return this.pollService.getPollResultByPollId(pollId);
    }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '获取用户的投票记录', description: '用户权限' })
    @Get('getUserPollRecords')
    @HttpCode(HttpStatus.OK)
    async getUserPollRecords(@Request() request) {
        if (!request.user) {
            throw new UnauthorizedException('用户未登录');
        }
        // console.log('request.user', request)
        const userId = request.user.id;
        return this.pollService.getPollRecordsByUserId(userId);
    }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '获取用户投票项目的可投票次数', description: '用户权限' })
    @Get('getUserPollTimes/:pollId')
    @HttpCode(HttpStatus.OK)
    async getUserPollTimes(@Param('pollId') pollId: string, @Request() request) {
        return this.pollService.getUserPollTimes(request.user.id, pollId);
    }


    @ApiOperation({ summary: '获取投票项目的投票用户', description: '无限制' })
    @Get('getPollUsers/:pollId')
    @HttpCode(HttpStatus.OK)
    async getPollUsersByPollId(@Param('pollId') pollId: string) {
        return this.pollService.getPollUsersByPollId(pollId);
    }
}
