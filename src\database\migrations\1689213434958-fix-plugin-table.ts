import { MigrationInterface, QueryRunner } from "typeorm";

export class fixPluginTable1689213434958 implements MigrationInterface {
    name = 'fixPluginTable1689213434958'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`chineseDescription\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`downloadCount\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`downloadUrl\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` CHANGE \`banner\` \`banner\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`pluginUpdatedTime\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`pluginUpdatedTime\` datetime NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`updatedTime\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`updatedTime\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`updatedTime\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`updatedTime\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`pluginUpdatedTime\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`pluginUpdatedTime\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` CHANGE \`banner\` \`banner\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`downloadUrl\``);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`downloadCount\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`chineseDescription\` text NULL`);
    }

}
