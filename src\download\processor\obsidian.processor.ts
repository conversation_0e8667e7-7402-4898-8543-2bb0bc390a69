import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { ObsidianPlugin } from '../entities/obsidian_plugin.entity';
import { Repository } from 'typeorm';
import { LATEST_OBSIDIAN_PLUGIN_STATS_LIST_URL, ObsidianPluginStats, PluginInfo, fetchWithRetry, getPluginLatestReleaseInfo } from '../utils/obsidianPluginUtils';
import { ObsidianPluginService } from '../services/obsidian_plugin.service';
import { DownloadService } from '../services/download.service';
import { Website } from 'src/website/entities/website.entity';
import { WebsiteService } from 'src/website/website.service';
import { getLatestThemeManifest } from '../utils/obsidianThemeUtils';
import { ObsidianTheme } from '../entities/obsidian_theme.entity';
import { ObsidianThemeService } from '../services/obsidian_theme.service';
import { ThemeManifest } from '../utils/obsidianThemeUtils';

@Processor('obsidian-download')
export class ObsidianDownloadProcessor {

    constructor(
        @InjectRepository(ObsidianPlugin) private obsidianPluginRepository: Repository<ObsidianPlugin>,
        @InjectRepository(ObsidianTheme) private obsidianThemeRepository: Repository<ObsidianTheme>,
        private readonly obsidianPluginService: ObsidianPluginService,
        private readonly obsidianThemeService: ObsidianThemeService,
        private readonly downloadService: DownloadService,
        private readonly websiteService: WebsiteService
    ) {

    }

    @Process("addObsidianPlugin")
    async addObsidianPlugin(job: Job<PluginInfo>) {
        const plugin = job.data
        const pluginDetail = await getPluginLatestReleaseInfo(plugin.repo)

        // 检查云端是否已经有最新版了
        let isExistInOss = false
        let downloadUrl = undefined
        try {
            isExistInOss = await this.downloadService.isObsidianPluginZipFileExist(pluginDetail.manifest.id + '-' + pluginDetail.tagName + '.zip') ?? false;
        } catch (e) {
            throw new Error("获取插件在 OSS 的信息失败：" + e)
        }

        const pluginsStatsResponse = await fetchWithRetry(LATEST_OBSIDIAN_PLUGIN_STATS_LIST_URL);
        const pluginsStats: ObsidianPluginStats = await pluginsStatsResponse.json();

        if (isExistInOss) {
            downloadUrl = "https://download.pkmer.cn/obsidian-plugins/" + pluginDetail.manifest.id + '-' + pluginDetail.tagName + '.zip'
            console.log("没重新上传")
        } else {
            downloadUrl = await this.obsidianPluginService.downloadAndUploadLatestPlugin(pluginDetail);
            console.log("重新上传了")
        }

        let downloadCount = 0

        try {
            downloadCount = pluginsStats[pluginDetail.manifest.id].downloads
        } catch (error) {
            console.log(error)
        }

        const newPlugin = new ObsidianPlugin();
        newPlugin.id = pluginDetail.manifest.id;
        newPlugin.name = pluginDetail.manifest.name;
        newPlugin.repo = pluginDetail.repo;
        newPlugin.author = pluginDetail.manifest.author;
        newPlugin.description = pluginDetail.manifest.description;
        newPlugin.version = pluginDetail.tagName;
        newPlugin.downloadUrl = downloadUrl;
        newPlugin.downloadCount = downloadCount;
        newPlugin.isDesktopOnly = pluginDetail.manifest.isDesktopOnly;
        newPlugin.pluginUpdatedTime = new Date(pluginDetail.updatedAt);
        newPlugin.source = 'official';

        await this.obsidianPluginRepository.insert(newPlugin);
    }

    @Process('updateObsidianPlugin')
    async updateObsidianPlugin(job: Job<{ pluginId: string; downloads: number }>) {
        const pluginId = job.data.pluginId
        const downloads = job.data.downloads

        const existPlugin = await this.obsidianPluginRepository.findOne({
            where: {
                id: pluginId
            }
        })

        if (!existPlugin) {
            throw new Error("逻辑有问题，需要更新的插件在数据库里居然没有")
        }

        const pluginDetail = await getPluginLatestReleaseInfo(existPlugin.repo)

        // 检查云端是否已经有最新版了
        let isExistInOss = false
        let downloadUrl = undefined
        try {
            isExistInOss = await this.downloadService.isObsidianPluginZipFileExist(existPlugin.id + '-' + pluginDetail.tagName + '.zip') ?? false;
        } catch (e) {
            throw new Error("获取插件在 OSS 的信息失败：" + e)
        }

        if (isExistInOss) {
            downloadUrl = "https://download.pkmer.cn/obsidian-plugins/" + existPlugin.id + '-' + pluginDetail.tagName + '.zip'
        } else {
            downloadUrl = await this.obsidianPluginService.downloadAndUploadLatestPlugin(pluginDetail);
        }

        if (existPlugin.source === "community") {
            await this.obsidianPluginRepository.update({ id: pluginId }, {
                downloadUrl: downloadUrl,
                version: pluginDetail.tagName,
                downloadCount: downloads,
                pluginUpdatedTime: new Date(pluginDetail.updatedAt),
                isDesktopOnly: pluginDetail.manifest.isDesktopOnly,
                description: pluginDetail.manifest.description,
                source: "official"
            });

            await this.websiteService.removeCommunityObsidianPlugin(existPlugin.repo)
        } else {
            await this.obsidianPluginRepository.update({ id: pluginId }, {
                downloadUrl: downloadUrl,
                version: pluginDetail.tagName,
                downloadCount: downloads,
                pluginUpdatedTime: new Date(pluginDetail.updatedAt),
                isDesktopOnly: pluginDetail.manifest.isDesktopOnly,
                description: pluginDetail.manifest.description
            });
        }
    }

    @Process("updateObsidianCommunityPlugin")
    async updateObsidianCommunityPlugin(job: Job<Website>) {
        let pluginRepo = job.data.content

        if (pluginRepo.startsWith("https://")) {
            pluginRepo = pluginRepo.replace("https://github.com/", "")
        }

        const existPlugin = await this.obsidianPluginRepository.findOne({
            where: {
                repo: pluginRepo
            }
        })

        if (existPlugin) {
            const pluginDetail = await getPluginLatestReleaseInfo(existPlugin.repo)

            // 检查云端是否已经有最新版了
            let isExistInOss = false
            let downloadUrl = undefined
            try {
                isExistInOss = await this.downloadService.isObsidianPluginZipFileExist(existPlugin.id + '-' + pluginDetail.tagName + '.zip') ?? false;
            } catch (e) {
                throw new Error("获取插件在 OSS 的信息失败：" + e)
            }

            if (isExistInOss) {
                downloadUrl = "https://download.pkmer.cn/obsidian-plugins/" + existPlugin.id + '-' + pluginDetail.tagName + '.zip'
            } else {
                downloadUrl = await this.obsidianPluginService.downloadAndUploadLatestPlugin(pluginDetail);
            }

            await this.obsidianPluginRepository.update({ id: existPlugin.id }, {
                downloadUrl: downloadUrl,
                version: pluginDetail.tagName,
                downloadCount: pluginDetail.downloadCount,
                pluginUpdatedTime: new Date(pluginDetail.updatedAt),
                isDesktopOnly: pluginDetail.manifest.isDesktopOnly,
                description: pluginDetail.manifest.description
            });
        } else {
            const pluginDetail = await getPluginLatestReleaseInfo(pluginRepo)

            // 检查云端是否已经有最新版了
            let isExistInOss = false
            let downloadUrl = undefined
            try {
                isExistInOss = await this.downloadService.isObsidianPluginZipFileExist(pluginDetail.manifest.id + '-' + pluginDetail.tagName + '.zip') ?? false;
            } catch (e) {
                throw new Error("获取插件在 OSS 的信息失败：" + e)
            }

            // const pluginsStatsResponse = await fetchWithRetry(LATEST_OBSIDIAN_PLUGIN_STATS_LIST_URL);
            // const pluginsStats: ObsidianPluginStats = await pluginsStatsResponse.json();

            if (isExistInOss) {
                downloadUrl = "https://download.pkmer.cn/obsidian-plugins/" + pluginDetail.manifest.id + '-' + pluginDetail.tagName + '.zip'
                console.log("没重新上传")
            } else {
                downloadUrl = await this.obsidianPluginService.downloadAndUploadLatestPlugin(pluginDetail);
                console.log("重新上传了")
            }

            const newPlugin = new ObsidianPlugin();
            newPlugin.id = pluginDetail.manifest.id;
            newPlugin.name = pluginDetail.manifest.name;
            newPlugin.repo = pluginDetail.repo;
            newPlugin.author = pluginDetail.manifest.author;
            newPlugin.description = pluginDetail.manifest.description;
            newPlugin.version = pluginDetail.tagName;
            newPlugin.downloadUrl = downloadUrl;
            newPlugin.downloadCount = 0;
            newPlugin.isDesktopOnly = pluginDetail.manifest.isDesktopOnly;
            newPlugin.pluginUpdatedTime = new Date(pluginDetail.updatedAt);
            newPlugin.source = 'community';

            await this.obsidianPluginRepository.insert(newPlugin);
        }
    }


    @Process("updateObsidianTheme")
    async updateObsidianTheme(job: Job<any>) {
        const { repo, name, screenshot, modes, author, downloads } = job.data

        const themeEntity = await this.obsidianThemeRepository.findOne({
            where: {
                repo
            }
        })

        // 获取主题repo最新的 manifest 信息
        let latestThemeManifest: ThemeManifest
        try {
            latestThemeManifest = await getLatestThemeManifest(repo)
            if (!latestThemeManifest || !latestThemeManifest.version) {
                throw new Error("主题没有manifest或者没有版本号")
            }
        } catch (e) {
            throw new Error("从GitHub获取主题最新manifest失败" + e)
        }

        if (themeEntity) {
            // 检查版本是否是最新
            if (themeEntity.version === latestThemeManifest.version) {
                //检查OSS是否存在对应版本的主题
                let isExistInOss = false
                try {
                    isExistInOss = await this.downloadService.isObsidianThemeZipFileExist(themeEntity.downloadUrl.replace('https://download.pkmer.cn/obsidian-themes/', '')) ?? false
                } catch (e) {
                    throw new Error("获取OSS主题信息失败")
                }

                if (!isExistInOss) {
                    try {
                        const downloadUrl = await this.obsidianThemeService.downloadAndUploadLatestTheme(latestThemeManifest, repo)
                        // const bannerUrl = await this.downloadAndUploadBannerImage(screenshot, repo)
                        // const avatarUrl = await this.downloadAndUploadAvatarImage(repo)

                        if (!downloadUrl) {
                            throw new Error('downloadUrl 为空')
                        }

                        await this.obsidianThemeRepository.update(themeEntity.id, {
                            version: latestThemeManifest.version,
                            downloadUrl,
                            // banner: bannerUrl,
                            // authorAvatar: avatarUrl,
                            updatedTime: new Date(),
                            downloadCount: downloads
                        })
                    } catch (e) {
                        throw new Error('重新获取云端Obsidian主题失败' + themeEntity.repo + e)
                    }
                }
            } else {
                //直接更新
                try {
                    const downloadUrl = await this.obsidianThemeService.downloadAndUploadLatestTheme(latestThemeManifest, repo)
                    // const bannerUrl = await this.downloadAndUploadBannerImage(screenshot, repo)
                    // const avatarUrl = await this.downloadAndUploadAvatarImage(repo)

                    if (!downloadUrl) {
                        throw new Error('downloadUrl 为空')
                    }

                    await this.obsidianThemeRepository.update(themeEntity.id, {
                        version: latestThemeManifest.version,
                        downloadUrl,
                        // banner: bannerUrl,
                        // authorAvatar: avatarUrl,
                        updatedTime: new Date(),
                        downloadCount: downloads
                    })
                } catch (e) {
                    throw new Error('重新获取云端Obsidian主题失败' + themeEntity.repo + e)
                }
            }
        } else {
            try {
                const downloadUrl = await this.obsidianThemeService.downloadAndUploadLatestTheme(latestThemeManifest, repo)
                const bannerUrl = await this.obsidianThemeService.downloadAndUploadBannerImage(screenshot, repo)
                const avatarUrl = await this.obsidianThemeService.downloadAndUploadAvatarImage(repo)

                if (!downloadUrl) {
                    throw new Error('上传主题失败，downloadUrl 为空' + latestThemeManifest + repo)
                }

                await this.obsidianThemeRepository.insert({
                    repo,
                    name,
                    banner: bannerUrl ? bannerUrl : '',
                    modes: modes.join(','),
                    author,
                    authorAvatar: avatarUrl ? avatarUrl : '',
                    version: latestThemeManifest.version,
                    downloadUrl,
                    downloadCount: downloads ?? 0,
                    updatedTime: new Date(),
                })
            } catch (e) {
                throw new Error('Obsidian主题插入失败' + repo + e)
            }
        }
    }
}
