import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityCondition } from 'src/utils/types/entity-condition.type';
import { IPaginationOptions } from 'src/utils/types/pagination-options';
import { Repository } from 'typeorm';
import { Widgets } from './entities/widgets.entity';
import { UpdateWidgetDto } from './dto/update-widget.dto';

@Injectable()
export class WidgetsService {
    constructor(@InjectRepository(Widgets) private widgetsRepository: Repository<Widgets>) { }

    findManyWithPagination(paginationOptions: IPaginationOptions) {
        return this.widgetsRepository.find({
            skip: (paginationOptions.page - 1) * paginationOptions.limit,
            take: paginationOptions.limit,
        })
    }

    findOne(fields: EntityCondition<Widgets>) {
        return this.widgetsRepository.findOne({
            where: fields
        })
    }

    update(id: number, updateProfileDto: UpdateWidgetDto) {
        return this.widgetsRepository.save(
            this.widgetsRepository.create({
                id,
                ...updateProfileDto,
            }),
        );
    }
}

