import fs from "fs";


fs.readFile("./community-plugin-stats.json", "utf8", (err, data) => {
    const content = JSON.parse(data);
    const testPlugins = [
        {
            id: "nldates-obsidian",
            version: "0.6.1",
        },
        {
            id: "obsidian-git",
            version: "2.20.6",
        },
        {
            id: "cm-show-whitespace-obsidian",
            version: "0.3.1",
        },
        {
            id: "calendar",
            version: "1.5.10",
        },
        {
            id: "mrj-text-expand",
            version: "0.11.2",
        },
        {
            id: "periodic-note",
            version: "0.0.17",
        },
        {
            id: "various-complements",
            version: "8.3.2",
        },
        {
            id: "obsidian-zoom",
            version: "1.1.2",
        },
        {
            id: "obsidian-apply-patterns",
            version: "2.1.2",
        },
        {
            id: "omnisearch",
            version: "1.14.2",
        },
        {
            id: "dataview",
            version: "0.5.56",
        }
    ];
    testPlugins.forEach((plugin) => {
        const pluginData = content[plugin.id];
        if (pluginData) {
        }
    });
});
