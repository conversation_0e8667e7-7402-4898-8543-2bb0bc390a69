import { EntityHelper } from "src/utils/entity-helper";
import { Column, CreateDateColumn, UpdateDateColumn, Entity, PrimaryGeneratedColumn, PrimaryColumn } from "typeorm";

@Entity()
export class Article extends EntityHelper {
    @PrimaryColumn()
    id: string

    @Column()
    name?: string;

    @Column({ default: 0 })
    visited: number;

    @Column({ default: 0 })
    like: number

    @Column({ default: 0 })
    subscribe: number

    @UpdateDateColumn()
    updatedAt: Date;
}