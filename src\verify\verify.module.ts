import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IsExist } from 'src/utils/validators/is-exists.validator';
import { UsersModule } from 'src/users/users.module';
import { Verify } from './entities/verify.entity';
import { VerifyProductService } from './verify-product.service';
import { VerifyProductController } from './verify-product.controller';
import { GoodsModule } from 'src/goods/goods.module';

@Module({
    imports: [TypeOrmModule.forFeature([Verify]), UsersModule, GoodsModule],
    providers: [IsExist, VerifyProductService],
    controllers: [VerifyProductController],
    exports: [VerifyProductService],
})
export class VerifyModule { }