import { MigrationInterface, QueryRunner } from "typeorm";

export class createMemosTable1682608729164 implements MigrationInterface {
    name = 'createMemosTable1682608729164'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`memo\` (\`id\` varchar(36) NOT NULL, \`creator_id\` varchar(255) NOT NULL, \`created_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`rowStatus\` enum ('NORMAL', 'ARCHIVED') NOT NULL DEFAULT 'NORMAL', \`content\` longtext NOT NULL, \`visiable\` enum ('PRIVATE', 'PROTECT', 'PUBLIC') NOT NULL DEFAULT 'PRIVATE', \`pinned\` tinyint NOT NULL DEFAULT 0, \`tags\` text NULL, \`creatorId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`plugin\` (\`id\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`repo\` varchar(255) NOT NULL, \`author\` varchar(255) NULL, \`banner\` varchar(255) NOT NULL, \`category\` varchar(255) NULL, \`score\` int NULL, \`description\` text NULL, \`chineseDescription\` text NULL, \`version\` varchar(255) NOT NULL, \`updatedTime\` varchar(255) NOT NULL, \`pluginUpdatedTime\` varchar(255) NOT NULL, \`downloadCount\` int NULL, \`readme\` longtext NULL, \`chineseReadme\` longtext NULL, INDEX \`IDX_9fad326649a503cc8122df59d9\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`supporter\` tinyint NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`order\` ADD \`amount\` float NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`memo\` ADD CONSTRAINT \`FK_c4677fe4aa77658512719c58ef4\` FOREIGN KEY (\`creatorId\`) REFERENCES \`user\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`memo\` DROP FOREIGN KEY \`FK_c4677fe4aa77658512719c58ef4\``);
        await queryRunner.query(`ALTER TABLE \`order\` DROP COLUMN \`amount\``);
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`supporter\``);
        await queryRunner.query(`DROP INDEX \`IDX_9fad326649a503cc8122df59d9\` ON \`plugin\``);
        await queryRunner.query(`DROP TABLE \`plugin\``);
        await queryRunner.query(`DROP TABLE \`memo\``);
    }

}
