import { cp } from "fs";
import { <PERSON><PERSON><PERSON><PERSON>el<PERSON> } from "src/utils/entity-helper";
import { Entity, Index } from "typeorm";
import { Column, CreateDateColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity()
export class DownloadRecords extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @CreateDateColumn()
    created_ts: Date

    @UpdateDateColumn()
    updated_ts: Date

    @Column()
    pluginId: string

    @Column()
    version: string

    @Index()
    @Column({ nullable: true, type: 'uuid' })
    creator: string

    @Column({ type: 'enum', enum: ['ObsidianPlugin', 'ObsidianTheme', 'ObsidianCSS', 'ObsidianScript', 'ZoteroPlugin', 'LogseqPlugin'], nullable: false })
    downloadType: string
}