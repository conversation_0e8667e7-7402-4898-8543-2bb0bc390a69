import { EntityHelper } from 'src/utils/entity-helper';
import { Entity, Column, PrimaryGeneratedColumn, Index, DeleteDateColumn, UpdateDateColumn, CreateDateColumn } from 'typeorm';

@Entity()
export class ActivationRecord extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Index()
    @Column({ type: 'uuid' })
    creatorId: string;

    @Column({ type: "enum", enum: ["thino", "templify", "media-extend"], nullable: false })
    type: "thino" | "templify" | "media-extend";

    @Column({ nullable: false })
    appid: string;

    @Column({nullable: false})
    appName: string

    @Column({ type: "longtext" })
    code: string

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}