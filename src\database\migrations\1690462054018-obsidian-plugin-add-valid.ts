import { MigrationInterface, QueryRunner } from "typeorm";

export class obsidianPluginAddValid1690462054018 implements MigrationInterface {
    name = 'obsidianPluginAddValid1690462054018'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`obsidian_plugin\` ADD \`valid\` tinyint NOT NULL DEFAULT 1`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`obsidian_plugin\` DROP COLUMN \`valid\``);
    }

}
