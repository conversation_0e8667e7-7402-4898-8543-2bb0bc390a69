import { EntityHelper } from "src/utils/entity-helper";
import { Column, CreateDateColumn, UpdateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity()
export class Widgets extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: number

    @Column({ nullable: false })
    name: string;

    @Column({ nullable: true })
    tags: string

    @Column({ nullable: false })
    banner: string

    @Column({ nullable: false })
    createPage: string

    @Column({ nullable: false })
    desc: string

    @Column({ nullable: false, type: 'float' })
    rating: number

    @Column({ nullable: false, type: 'float' })
    price: number

    @Column({ nullable: false })
    status?: boolean;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}