import { EntityHelper } from 'src/utils/entity-helper';
import { Entity, Column, PrimaryGeneratedColumn, Index, DeleteDateColumn, UpdateDateColumn, CreateDateColumn } from 'typeorm';

@Entity()
export class CheckoutOrder extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    //一个订单对应一个创建者用户，关联User实体
    @Index()
    @Column({ nullable: false, type: 'uuid' })
    creatorId: string;

    @Column({ nullable: false, type: 'uuid' })
    goodsId: string

    @Column({ nullable: false })
    checkoutOrderId: string

    @Column()
    goodsName: string

    @Column()
    goodsCount: number;

    @Column({ type: 'float' })
    amount: number | null;

    @Column()
    currency: string

    @Column()
    email: string

    // 'pending' | 'paid' | 'refunded' | 'canceled'
    @Column({ default: 'pending' })
    status: string

    @Column()
    country: string

    @Column({ nullable: true })
    description?: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn()
    deletedAt: Date;
}