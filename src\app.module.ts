import * as path from 'path';
import { Module } from '@nestjs/common';
import appConfig from './config/app.config';
import { ConfigModule } from '@nestjs/config';
import { DataSource } from 'typeorm';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TypeOrmConfigService } from './database/typeorm-config.service';
import databaseConfig from './config/database.config';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { FilesModule } from './files/files.module';
import { MailModule } from './mail/mail.module';
import { ForgotModule } from './forgot/forgot.module';
import { MailConfigService } from './mail/mail-config.service';
import { MailerModule } from '@nestjs-modules/mailer';
import { I18nModule } from 'nestjs-i18n/dist/i18n.module';
import { HeaderResolver } from 'nestjs-i18n';
import { ConfigService } from '@nestjs/config';
import authConfig from './config/auth.config';
import mailConfig from './config/mail.config';
import fileConfig from './config/file.config';
import smsConfig from './config/sms.config';
import wechatConfig from './config/wechat.config';
import weworkConfig from './config/wework.config';

import { AdminModule } from '@adminjs/nestjs';
import * as AdminJSTypeorm from '@adminjs/typeorm'
import AdminJS from 'adminjs'
import { checkoutOrderResource, orderResource, pluginResource, thinoActivateResource, thinoResource, userResource } from './admin/resource';

import { Role } from './roles/entities/role.entity';
import { Status } from './statuses/entities/status.entity';
import { WidgetsModule } from './widgets/widgets.module';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { SmsModule } from './sms/sms.module';
import { PaymentModule } from './payment/payment.module';
import { OrdersModule } from './orders/orders.module';
import { GoodsModule } from './goods/goods.module';
import { WechatModule } from './wechat/wechat.module';
import { PollModule } from './poll/poll.module';
import redisConfig from './config/redis.config';
import paymentConfig from './config/payment.config';
import { Poll } from './poll/entities/poll.entity';
import { Goods } from './goods/entities/goods.entity';
import { PollRecords } from './poll/entities/poll_records.entity';
import { OssModule } from './oss/oss.module';
import { ScheduleModule } from './schedule/schedule.module';
import { DownloadModule } from './download/download.module';
import { WebsiteModule } from './website/website.module';
import ossConfig from './config/oss.config';
import { Website } from './website/entities/website.entity';
import { ZoteroPlugin } from './download/entities/zotero_plugin.entity';
import { BullModule } from '@nestjs/bull';
import { VerifyModule } from './verify/verify.module';
import { Verify } from './verify/entities/verify.entity';

const authenticate = async (email: string, password: string) => {
    if (email === process.env.ADMIN_EMAIL && password === process.env.ADMIN_PASSWORD) {
        return Promise.resolve({
            email: process.env.ADMIN_EMAIL,
            password: process.env.ADMIN_PASSWORD,
        })
    }
    return null
}

AdminJS.registerAdapter({
    Resource: AdminJSTypeorm.Resource,
    Database: AdminJSTypeorm.Database,
})

const IS_PROD = process.env.NODE_ENV === 'dev' ? false : true;
@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            load: [
                appConfig,
                databaseConfig,
                authConfig,
                mailConfig,
                fileConfig,
                redisConfig,
                smsConfig,
                paymentConfig,
                wechatConfig,
                weworkConfig,
                ossConfig,
            ],
            envFilePath: IS_PROD ? '.env.prod' : '.env.dev',
        }),
        TypeOrmModule.forRootAsync({
            useClass: TypeOrmConfigService,
            dataSourceFactory: async (options) => {
                const dataSource = await new DataSource(options).initialize();
                return dataSource;
            },
        }),
        RedisModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => {
                // console.log(configService.get('redis'))
                return { config: configService.get('redis') }
            }
        }),
        BullModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => {
                return {
                    redis: {
                        host: configService.get('redis.host'),
                        port: configService.get('redis.port'),
                        db: 1,
                        password: configService.get('redis.password')
                    }
                }
            }
        }),
        MailerModule.forRootAsync({
            useClass: MailConfigService,
        }),
        I18nModule.forRootAsync({
            useFactory: (configService: ConfigService) => ({
                fallbackLanguage: configService.get('app.fallbackLanguage'),
                loaderOptions: {
                    path: path.join(__dirname, '/i18n/'),
                    watch: true,
                },
            }),
            resolvers: [
                {
                    use: HeaderResolver,
                    useFactory: (configService: ConfigService) => {
                        return [configService.get('app.headerLanguage')];
                    },
                    inject: [ConfigService],
                },
            ],
            imports: [ConfigModule],
            inject: [ConfigService],
        }),
        AdminModule.createAdminAsync({
            useFactory: () => ({
                adminJsOptions: {
                    rootPath: '/admin',
                    resources: [userResource, thinoResource, thinoActivateResource, Website, Goods, orderResource, checkoutOrderResource, pluginResource, ZoteroPlugin, Role, Status, Poll, PollRecords, Verify],
                    settings: {
                        defaultPerPage: 30
                    }
                },
                auth: {
                    authenticate,
                    cookieName: 'adminjs',
                    cookiePassword: 'secret'
                },
                sessionOptions: {
                    resave: false,
                    saveUninitialized: true,
                    secret: 'secret'
                }
            }),
        }),
        ForgotModule,
        UsersModule,
        AuthModule,
        FilesModule,
        MailModule,
        ForgotModule,
        WidgetsModule,
        RedisModule,
        SmsModule,
        PaymentModule,
        OrdersModule,
        GoodsModule,
        WechatModule,
        PollModule,
        OssModule,
        ScheduleModule,
        DownloadModule,
        WebsiteModule,
        VerifyModule,
    ],
})
export class AppModule { }
