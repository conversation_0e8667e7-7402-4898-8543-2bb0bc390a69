import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpStatus, HttpException } from '@nestjs/common';
// import OSS from 'ali-oss'
import { OSSEntity } from './entities/oss.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { UsersService } from 'src/users/users.service';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis'
import { IPaginationOptions } from 'src/utils/types/pagination-options';
import { randomUUID } from 'crypto';

@Injectable()
export class OssService {
    oss: any
    constructor(
        private readonly configService: ConfigService,
        private readonly usersService: UsersService,
        @InjectRedis() private readonly redis: Redis,
        @InjectRepository(OSSEntity) private ossRepository: Repository<OSSEntity>
    ) {
        const OSS = require('ali-oss');
        this.oss = new OSS({
            region: this.configService.get('oss.ossRegion'),
            accessKeyId: this.configService.get('oss.ossAccessKeyId'),
            accessKeySecret: this.configService.get('oss.ossAccessKeySecret'),
            bucket: this.configService.get('oss.ossDefaultBucket'),
        })
    }

    //上传用户头像图片到OSS
    async uploadUserAvatar(userId: string, file: any): Promise<any> {
        const user = await this.usersService.findOne({ id: userId });
        const userName = user.nickName;
        if (!file) {
            throw new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: '请选择文件上传',
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            );
        }

        //限制格式为jpeg,如果是png，jpg转换为jpeg
        if (file.originalname.match(/\.(png)$/i)) {
            const sharp = require('sharp');
            file.buffer = await sharp(file.buffer)
                .jpeg()
                .toBuffer();
        } else if (!file.originalname.match(/\.(jpg|jpeg)$/i)) {
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `不受支持的文件类型`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }

        //限制文件大小在1MB以内
        if (file.size > 1024 * 1024 * 1) {
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `文件大小不能超过1MB`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }

        //上传到OSS, 文件名为用户名
        try {
            this.oss.useBucket('pkmer');
            await this.oss.put(`/covers/${userName}.jpeg`, file.buffer);
        } catch (e) {
            console.log(e)
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `上传失败,请重试或者联系网站管理员`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }
        const url = 'https://cdn.pkmer.cn/covers/' + userName + '.jpeg'
        try {
            await this.usersService.update(userId, { avatar: url });
        } catch (e) {
            console.log(e)
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `更新到数据库失败,请重试或者联系网站管理员`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }
        return url
    }

    //上传图片到OSS
    async uploadFile(creator: string, file: any): Promise<any> {
        if (!file) {
            throw new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: '请选择文件上传',
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            );
        }

        //限制格式
        if (
            !file.originalname.match(/\.(jpg|jpeg|png|gif)$/i)
        ) {
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `不受支持的文件类型`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }

        //限制文件大小在10MB以内
        if (file.size > 1024 * 1024 * 10) {
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `文件大小不能超过10MB`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }

        //用redis限非会员用户一共能上传250MB的文件
        const user = await this.usersService.findOne({ id: creator });
        if (user.type === 'user') {
            const totalSize = await this.redis.get(`user:${creator}:file:totalSize`);
            if (totalSize && parseInt(totalSize) > 1024 * 1024 * 250) {
                return new HttpException(
                    {
                        status: HttpStatus.UNPROCESSABLE_ENTITY,
                        errors: {
                            file: `非会员用户一共能上传250MB的文件`,
                        },
                    },
                    HttpStatus.UNPROCESSABLE_ENTITY,
                )
            }
        }


        //用redis限制会员用户一共能上传15GB的文件
        if (user.type === 'insider') {
            const totalSize = await this.redis.get(`user:${creator}:file:totalSize`);
            if (totalSize && parseInt(totalSize) > 1024 * 1024 * 1024 * 15) {
                return new HttpException(
                    {
                        status: HttpStatus.UNPROCESSABLE_ENTITY,
                        errors: {
                            file: `会员用户一共能上传15GB的文件`,
                        },
                    },
                    HttpStatus.UNPROCESSABLE_ENTITY,
                )
            }
        }



        //保存文件信息到数据库
        const ossItem = new OSSEntity();
        ossItem.name = file.originalname;
        ossItem.creator = creator;

        //生成一个uuid
        const uuid = randomUUID();
        const basePath = 'https://img.pkmer.cn/memos/'
        const extName = file.originalname.match(/\.(jpg|jpeg|png|gif)$/i)[0];
        ossItem.path = 'https://img.pkmer.cn/memos/' + uuid + extName;

        //判断数据库中是否存在相同path的文件
        const isOssItemExist = await this.ossRepository.findOne({ where: { path: ossItem.path } });
        if (isOssItemExist) {
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `文件id重复，请重试`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }

        try {
            await this.oss.put('memos/' + uuid + extName, file.buffer);
            //redis增加用户上传文件大小，TTL有效期为一年
            await this.redis.incrby(`user:${creator}:file:totalSize`, file.size);
            await this.redis.expire(`user:${creator}:file:totalSize`, 60 * 60 * 24 * 365);
        } catch (err) {
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `文件上传失败，请重试`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }
        return await this.ossRepository.save(ossItem);
    }

    //根据用户id分页获取用户上传的文件列表
    async getFilesByCreator(creator: string, paginationOptions: IPaginationOptions): Promise<any> {
        const files = await this.ossRepository.find({
            where: { creator: creator }, skip: (paginationOptions.page - 1) * paginationOptions.limit,
            take: paginationOptions.limit,
        });
        return files;
    }

    //根据文件path删除文件
    async deleteFileById(creator: string, id: string): Promise<any> {
        const file = await this.ossRepository.findOne({ where: { creator: creator, id: id } });
        if (!file) {
            return new HttpException(
                {
                    status: HttpStatus.UNPROCESSABLE_ENTITY,
                    errors: {
                        file: `文件不存在`,
                    },
                },
                HttpStatus.UNPROCESSABLE_ENTITY,
            )
        }
        await this.ossRepository.delete({ id: id });
        await this.oss.delete('memos/' + file.path.split('memos/')[1]);
        return {
            code: HttpStatus.OK,
            message: '删除成功'
        }
    }

}
