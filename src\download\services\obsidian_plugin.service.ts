import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { UsersService } from 'src/users/users.service';
import { Repository } from 'typeorm';
import { ObsidianPlugin } from '../entities/obsidian_plugin.entity';
import { LATEST_OBSIDIAN_PLUGIN_LIST_URL, LATEST_OBSIDIAN_PLUGIN_STATS_LIST_URL, ObsidianPluginStats, PluginDetail, PluginInfo, fetchWithRetry, getPluginLatestReleaseInfo, getReleaseByVersion } from '../utils/obsidianPluginUtils';
import { IPaginationOptions } from 'src/utils/types/pagination-options';
import { DownloadRecords } from '../entities/download_records.entity';
import { DownloadService } from './download.service';
import Redis from 'ioredis';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import dayjs from 'dayjs';
import { WebsiteService } from 'src/website/website.service';

@Injectable()
export class ObsidianPluginService {
    constructor(
        private readonly usersService: UsersService,
        private readonly configService: ConfigService,
        private readonly downloadService: DownloadService,
        private readonly websiteService: WebsiteService,
        @InjectRepository(ObsidianPlugin) private obsidianPluginRepository: Repository<ObsidianPlugin>,
        @InjectRepository(DownloadRecords) private downloadRecordsRepository: Repository<DownloadRecords>,
        @InjectRedis() private readonly redisClient: Redis,
        @InjectQueue('obsidian-download') private readonly obsidainPluginDownloadQueue: Queue
    ) { }

    async downloadAndUploadLatestPlugin(pluginDetail: PluginDetail): Promise<string> {
        const { mainJsBrowserDownloadUrl, stylesCssBrowserDownloadUrl } = pluginDetail

        if (stylesCssBrowserDownloadUrl) {
            const mainJsResponse = await fetchWithRetry(mainJsBrowserDownloadUrl);
            const styleCssResponse = await fetchWithRetry(stylesCssBrowserDownloadUrl);

            const manifest = pluginDetail.manifest;

            const JSZip = require('jszip');
            const zip = new JSZip();
            zip.file('main.js', await mainJsResponse.text());
            zip.file('styles.css', await styleCssResponse.text());
            zip.file('manifest.json', JSON.stringify(manifest));
            const content = await zip.generateAsync({ type: 'blob' });
            const buffer = await content.arrayBuffer();

            const uploadStatus = await this.downloadService.uploadObsidianPluginZipFile(pluginDetail.manifest.id + '-' + pluginDetail.tagName + '.zip', Buffer.from(buffer));
            if (!uploadStatus) {
                return uploadStatus;
            }
            return 'https://download.pkmer.cn/obsidian-plugins/' + pluginDetail.manifest.id + '-' + pluginDetail.tagName + '.zip';
        } else {
            const mainJsResponse = await fetchWithRetry(mainJsBrowserDownloadUrl);

            const manifest = pluginDetail.manifest;

            const JSZip = require('jszip');
            const zip = new JSZip();
            zip.file('main.js', await mainJsResponse.text());
            zip.file('manifest.json', JSON.stringify(manifest));
            const content = await zip.generateAsync({ type: 'blob' });
            const buffer = await content.arrayBuffer();

            const uploadStatus = await this.downloadService.uploadObsidianPluginZipFile(pluginDetail.manifest.id + '-' + pluginDetail.manifest.version + '.zip', Buffer.from(buffer));
            if (!uploadStatus) {
                return uploadStatus;
            }
            return 'https://download.pkmer.cn/obsidian-plugins/' + pluginDetail.manifest.id + '-' + pluginDetail.tagName + '.zip';

        }
    }

    async getObsidianPluginNeedAdd(): Promise<PluginInfo[]> {
        try {
            const existObsidianPlugins = await this.obsidianPluginRepository.createQueryBuilder("obsidian_plugin").select(["obsidian_plugin.id"]).getMany()
            let allObsidianPlugins: PluginInfo[] | undefined = undefined
            const pluginsInfoResponse = await fetchWithRetry(LATEST_OBSIDIAN_PLUGIN_LIST_URL)
            allObsidianPlugins = await pluginsInfoResponse.json() as PluginInfo[]

            const needAddPlugin = allObsidianPlugins.filter((plugin) => {
                return !existObsidianPlugins.find((el) => el.id === plugin.id)
            })

            return needAddPlugin
        } catch (error) {
            throw new Error("从Obsidian官方获取插件列表失败：" + error)
        }
    }

    async getObsidianPluginNeedUpdate(): Promise<Array<{ pluginId: string; downloads: number }>> {
        try {
            const pluginsStatsResponse = await fetchWithRetry(LATEST_OBSIDIAN_PLUGIN_STATS_LIST_URL);
            const pluginsStats: ObsidianPluginStats = await pluginsStatsResponse.json();
            const existPlugins = await this.obsidianPluginRepository.createQueryBuilder("obsidian_plugin").select(["obsidian_plugin.id", "obsidian_plugin.pluginUpdatedTime"]).getMany()

            let needUpdatePlugin: Array<{ pluginId: string; downloads: number }> = []

            for (let plugin in pluginsStats) {
                // const exsitPlugin = await this.obsidianPluginRepository.findOne({ where: { id: plugin } })
                const currentPlugin = existPlugins.find((item) => item.id === plugin)
                if (!currentPlugin) continue

                if (dayjs(pluginsStats[plugin].updated).diff(dayjs(currentPlugin.pluginUpdatedTime), "day") > 1) {
                    needUpdatePlugin.push({ pluginId: plugin, downloads: pluginsStats[plugin].downloads })
                }
            }

            return needUpdatePlugin

        } catch (error) {
            throw new Error("从Obsidian官方获取插件更新信息失败：" + error)
        }

    }

    async updateObsidianPlugins() {
        const needAddPlugins = await this.getObsidianPluginNeedAdd()

        needAddPlugins.forEach(async (plugin) => {
            await this.obsidainPluginDownloadQueue.add("addObsidianPlugin", plugin, {
                jobId: plugin.id,
                delay: 500,
                removeOnComplete: true,
                removeOnFail: 10,
                timeout: 180000
            })
        })

        const needUpdatePlugins = await this.getObsidianPluginNeedUpdate()
        // const needUpdatePlugins = [{
        //     pluginId: "hotkeysplus-obsidian",
        //     downloads: 2
        // }, {
        //     pluginId: "nldates-obsidian",
        //     downloads: 3
        // }]
        needUpdatePlugins.forEach(async (pluginSimpleInfo) => {
            await this.obsidainPluginDownloadQueue.add("updateObsidianPlugin", pluginSimpleInfo, {
                jobId: pluginSimpleInfo.pluginId,
                delay: 500,
                removeOnComplete: true,
                removeOnFail: 10,
                timeout: 180000
            })
        })

        await this.updateCommunityObsidianPlugins()

        // console.log("plugin", needUpdatePlugins)
        return true
    }

    async updateCommunityObsidianPlugins() {
        const communityPlugins = await this.websiteService.getCommunityObsidianPlugins()
        communityPlugins.forEach(async (plugin) => {
            await this.obsidainPluginDownloadQueue.add('updateObsidianCommunityPlugin', plugin, {
                jobId: plugin.content,
                delay: 500,
                removeOnComplete: true,
                removeOnFail: 10,
                timeout: 180000
            })
        })
    }

    async generateSignedUrl(userId: string, pluginId: string, version: string, shouldUseVersion: boolean = false) {
        const user = await this.usersService.findOne({ id: userId });
        // 判断用户是否存在
        if (!user) {
            throw new HttpException('用户未找到', HttpStatus.NOT_FOUND);
        }
        // 判断插件是否存在
        const isPluginExist = await this.obsidianPluginRepository.findOne({
            where: {
                id: pluginId
            }
        });
        if (!isPluginExist) {
            throw new HttpException('抱歉，这个插件貌似出现了问题，请反馈到 Pkmer！', HttpStatus.NOT_FOUND);
        }
        // 下载次数限制
        const redisKey = await this.downloadService.limitDownloadTimes(user);
        if (!redisKey) {
            return {
                code: HttpStatus.FORBIDDEN,
                message: "下载次数超出限制"
            };
        }
        // 确定下载链接和版本号
        let downloadUrl: string;
        let finalVersion: string;
        const downloadDomain = 'https://download.pkmer.cn'; // 或者使用 OSS 默认域名 'https://pkmer-download.oss-cn-beijing.aliyuncs.com'
        if (shouldUseVersion && version && version.trim() !== '') {
            // 如果 useVersion 为 true，且版本号有效，则使用传入的版本号构建下载链接
            finalVersion = version;
            downloadUrl = `${downloadDomain}/obsidian-plugins/${pluginId}-${version}.zip`;
            // 可选：验证指定版本文件是否存在于 OSS
            // const filePath = `${pluginId}-${version}.zip`;
            // const isPluginInOss = await this.downloadService.isObsidianPluginZipFileExist(filePath);
            // if (!isPluginInOss) {
            //     throw new HttpException(
            //         `抱歉，版本 ${version} 的插件不存在于云端，请检查版本号或反馈到 Pkmer！`,
            //         HttpStatus.NOT_FOUND
            //     );
            // }
        } else {
            // 默认逻辑：使用数据库中的最新版本
            finalVersion = isPluginExist.version;
            downloadUrl = isPluginExist.downloadUrl || `${downloadDomain}/obsidian-plugins/${pluginId}-${finalVersion}.zip`;
        
        }
        // 生成 CDN 签名 URL，http://DomainName/Filename?auth_key={<timestamp>-rand-uid-<md5hash>} 有效时长900秒
        const privateKey = this.configService.get('oss.cdnPrivateKey');
        const timestamp = Math.round(new Date().getTime() / 1000);
        const uri = downloadUrl.replace(downloadDomain, '');
        // 随机数，建议使用不含短横线的 uuid
        const { v4: uuidv4 } = require('uuid');
        const rand = uuidv4().replace(/-/g, '');
        const uid = '0';
        // 通过 MD5 算法计算出的字符串
        const crypto = require('crypto');
        const md5hash = crypto.createHash('md5').update(`${uri}-${timestamp}-${rand}-${uid}-${privateKey}`).digest('hex');
        const auth_key = `${timestamp}-${rand}-${uid}-${md5hash}`;
        const signedUrl = downloadUrl + '?auth_key=' + auth_key;
        // Redis 设置下载次数记录
        await this.redisClient.incr(redisKey);
        // 增加插件下载计数
        await this.obsidianPluginRepository.increment({ id: isPluginExist.id }, 'pkmerDownloadCount', 1);
        // 记录下载日志，使用最终确定的版本号
        await this.downloadRecordsRepository.insert({
            creator: userId,
            downloadType: 'ObsidianPlugin',
            pluginId: isPluginExist.id,
            version: finalVersion
        });
        return signedUrl;
    }

    // async getLeftDownloadCount(userId: string) {
    //     const redisKey = 'download:' + userId;
    //     let redisValue = await this.redisClient.get(redisKey);
    //     if (!redisValue) {
    //         redisValue = 1
    //     }

    //     const user = await this.usersService.findOne({ id: userId });
    //     if (user.type === 'insider') {
    //         return 200 - Number(redisValue);
    //     } else if((user.email || user.phone) || user.unionid) {
    //         return 15 - Number(redisValue);
    //     }else if(!(user.email || user.phone || user.unionid)) {
    //         return 5 - Number(redisValue);
    //     }
    // }

    async getTop20Plugins() {
        const top20Plugins = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .orderBy('obsidian_plugin.downloadCount', 'DESC')
            .where('obsidian_plugin.valid = :valid', { valid: true })
            .limit(20)
            .getMany();
        const getThinoPlugin = await this.obsidianPluginRepository.createQueryBuilder("obsidian_plugin").where("obsidian_plugin.id = :id", { id: "obsidian-memos" }).getMany()
        return [...top20Plugins, ...getThinoPlugin];
    }

    async getAllPlugins(userId: string) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        const allPlugins = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .orderBy('obsidian_plugin.downloadCount', 'DESC')
            .where('obsidian_plugin.valid = :valid', { valid: true })
            .getMany();
        //缓存一小时
        return allPlugins;
    }

    async getPluginsPaginated(userId: string, page: number = 1, limit: number = 10, sortBy: string = 'downloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC') {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'downloadCount'; // 默认排序字段
        }

        const [plugins, total] = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .orderBy(`obsidian_plugin.${sortBy}`, sortOrder)
            .where('obsidian_plugin.valid = :valid', { valid: true })
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();

        return {
            data: plugins,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder
            }
        };
    }

    async searchPluginsPaginated(userId: string, searchText: string, page: number = 1, limit: number = 10, sortBy: string = 'downloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC') {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        searchText = searchText.toLowerCase();

        // 验证排序字段是否有效
        const validSortFields = ['downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'downloadCount'; // 默认排序字段
        }

        const allPlugins = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .where('obsidian_plugin.valid = :valid', { valid: true })
            .getMany();

        let filteredPlugins = searchText.length < 1 ? allPlugins : allPlugins.filter(
            (plugin) =>
                plugin.id.toLowerCase().includes(searchText) || 
                plugin.name.toLowerCase().includes(searchText) || 
                plugin.author.toLowerCase().includes(searchText) || 
                (plugin.description?.toLowerCase().includes(searchText)) || 
                (plugin.chineseDescription?.toLowerCase().includes(searchText)) || 
                (plugin.tags?.toLowerCase().includes(searchText))
        );

        const total = filteredPlugins.length;
        
        // 根据sortBy和sortOrder对插件进行排序
        filteredPlugins = filteredPlugins.sort((a, b) => {
            if (sortOrder === 'ASC') {
                return a[sortBy] > b[sortBy] ? 1 : -1;
            } else {
                return a[sortBy] < b[sortBy] ? 1 : -1;
            }
        });
        
        // 分页处理
        filteredPlugins = filteredPlugins.slice((page - 1) * limit, page * limit);

        return {
            data: filteredPlugins,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder
            }
        };
    }

    async getPluginTags(userId: string) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        const allPlugins = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .where('obsidian_plugin.valid = :valid', { valid: true })
            .getMany();

        // 从所有插件中提取标签并计数
        let tagsMap = new Map<string, number>();
        allPlugins.forEach(plugin => {
            if (plugin.tags && typeof plugin.tags === 'string') {
                let tags = plugin.tags.split(',').map(tag => tag.trim());
                tags = tags.filter(tag => tag !== ''); // 过滤掉空字符串
                tags.forEach(tag => {
                    if (tagsMap.has(tag)) {
                        tagsMap.set(tag, tagsMap.get(tag) + 1);
                    } else {
                        tagsMap.set(tag, 1);
                    }
                });
            }
        });

        // 将Map转换为数组，并按照标签数量降序排序
        const tagsArray = Array.from(tagsMap).map(([tag, count]) => ({
            tag,
            count
        })).sort((a, b) => b.count - a.count);

        return tagsArray;
    }

    async getPluginsByTag(userId: string, tag: string, page: number = 1, limit: number = 10, sortBy: string = 'downloadCount', sortOrder: 'ASC' | 'DESC' = 'DESC', fuzzyMatch: boolean = false) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'downloadCount'; // 默认排序字段
        }

        const allPlugins = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .where('obsidian_plugin.valid = :valid', { valid: true })
            .getMany();

        // 筛选包含特定标签的插件，支持模糊匹配
        let filteredPlugins = allPlugins.filter(plugin => {
            if (plugin.tags && typeof plugin.tags === 'string') {
                const tags = plugin.tags.split(',').map(t => t.trim());
                if (fuzzyMatch) {
                    // 模糊匹配：只要标签包含搜索词即可
                    return tags.some(t => t.toLowerCase().includes(tag.toLowerCase()));
                } else {
                    // 精确匹配：标签必须完全匹配
                    return tags.includes(tag);
                }
            }
            return false;
        });

        const total = filteredPlugins.length;
        
        // 根据sortBy和sortOrder对插件进行排序
        filteredPlugins = filteredPlugins.sort((a, b) => {
            if (sortOrder === 'ASC') {
                return a[sortBy] > b[sortBy] ? 1 : -1;
            } else {
                return a[sortBy] < b[sortBy] ? 1 : -1;
            }
        });
        
        // 分页处理
        filteredPlugins = filteredPlugins.slice((page - 1) * limit, page * limit);

        return {
            data: filteredPlugins,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder,
                tag,
                fuzzyMatch
            }
        };
    }

    findManyWithPagination(userId: string, paginationOptions: IPaginationOptions) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }
        return this.obsidianPluginRepository.find({
            skip: (paginationOptions.page - 1) * paginationOptions.limit,
            take: paginationOptions.limit,
        });
    }

    findOne(keyword: string, limit: number, userId: string) {

        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }
        //根据关键词模糊搜索name，author，description
        return this.obsidianPluginRepository.createQueryBuilder('plugin')
            .where('plugin.name like :keyword', { keyword: `%${keyword}%` })
            .orWhere('plugin.author like :keyword', { keyword: `%${keyword}%` })
            .orWhere('plugin.description like :keyword', { keyword: `%${keyword}%` })
            .limit(limit)
            .getMany();
    }

    async getInstalledPluginsPaginated(
        userId: string,
        pluginIds: string[],
        page: number = 1,
        pageSize: number = 24,
        sortBy: string = "downloadCount",
        sortOrder: string = "DESC"
    ) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'downloadCount'; // 默认排序字段
        }

        const [plugins, total] = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .where('obsidian_plugin.id IN (:...pluginIds)', { pluginIds })
            .andWhere('obsidian_plugin.valid = :valid', { valid: true })
            .orderBy(`obsidian_plugin.${sortBy}`, sortOrder === 'ASC' ? 'ASC' : 'DESC')
            .skip((page - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();

        return {
            data: plugins,
            meta: {
                page,
                pageSize,
                total,
                totalPages: Math.ceil(total / pageSize),
                sortBy,
                sortOrder
            }
        };
    }

    async getOutdatedPluginsPaginated(
        userId: string,
        plugins: { id: string, version: string }[],
        page: number = 1,
        pageSize: number = 24,
        sortBy: string = "downloadCount",
        sortOrder: string = "DESC"
    ) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }

        // 验证排序字段是否有效
        const validSortFields = ['downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'downloadCount'; // 默认排序字段
        }

        // 获取所有需要检查的插件ID
        const pluginIds = plugins.map(p => p.id);
        
        // 查询这些插件的最新信息
        const [latestPlugins, total] = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .where('obsidian_plugin.id IN (:...pluginIds)', { pluginIds })
            .andWhere('obsidian_plugin.valid = :valid', { valid: true })
            .orderBy(`obsidian_plugin.${sortBy}`, sortOrder === 'ASC' ? 'ASC' : 'DESC')
            .skip((page - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();

        // 过滤出需要更新的插件
        const outdatedPlugins = latestPlugins.filter(plugin => {
            const installedPlugin = plugins.find(p => p.id === plugin.id);
            if (!installedPlugin) return false;
            
            // 比较版本号
            const installedVersion = installedPlugin.version.split('.').map(Number);
            const latestVersion = plugin.version.split('.').map(Number);
            
            for (let i = 0; i < Math.max(installedVersion.length, latestVersion.length); i++) {
                const installed = installedVersion[i] || 0;
                const latest = latestVersion[i] || 0;
                if (latest > installed) return true;
                if (latest < installed) return false;
            }
            return false;
        });

        return {
            data: outdatedPlugins,
            meta: {
                page,
                pageSize,
                total: outdatedPlugins.length,
                totalPages: Math.ceil(outdatedPlugins.length / pageSize),
                sortBy,
                sortOrder
            }
        };
    }

    async getPluginsBySource(
        userId: string,
        source: 'official' | 'community',
        page: number = 1,
        limit: number = 10,
        sortBy: string = 'downloadCount',
        sortOrder: 'ASC' | 'DESC' = 'DESC'
    ) {
        if (!this.downloadService.limitApiCallTimes(userId)) {
            throw new HttpException('API调用次数过多，请稍候重试', HttpStatus.FORBIDDEN);
        }
    
        // 验证排序字段是否有效
        const validSortFields = ['downloadCount', 'id', 'name', 'author', 'version', 'pluginUpdatedTime', 'pkmerDownloadCount'];
        if (!validSortFields.includes(sortBy)) {
            sortBy = 'downloadCount'; // 默认排序字段
        }
    
        const [plugins, total] = await this.obsidianPluginRepository
            .createQueryBuilder('obsidian_plugin')
            .orderBy(`obsidian_plugin.${sortBy}`, sortOrder)
            .where('obsidian_plugin.valid = :valid AND obsidian_plugin.source = :source', { valid: true, source })
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();
    
        return {
            data: plugins,
            meta: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                sortBy,
                sortOrder,
                source
            }
        };
    }
    
}