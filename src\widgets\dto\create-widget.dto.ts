import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export class CreateWidgetDto {
    @ApiProperty({ example: 'ColorfulClock' })
    name: string

    @ApiProperty({ example: 'tag1,tag2' })
    tags: string

    @ApiProperty()
    @IsNotEmpty()
    banner: string

    @ApiProperty({ example: '/products/widget/ColorfulClock' })
    @IsNotEmpty()
    createPage: string

    @ApiProperty()
    @IsNotEmpty()
    desc: string

    @ApiProperty({ type: 'number' })
    @IsNotEmpty()
    rating: number

    @ApiProperty({ type: 'number' })
    @IsNotEmpty()
    price: number

    @ApiProperty()
    @IsNotEmpty()
    stauts: boolean


}