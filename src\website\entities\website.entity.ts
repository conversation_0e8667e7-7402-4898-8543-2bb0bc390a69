import { EntityHelper } from "src/utils/entity-helper";
import { Column, CreateDateColumn, UpdateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity()
export class Website extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: number

    @Column({ nullable: false })
    name: string;

    @Column({ nullable: true, type: 'enum', enum: ['wechat_reply', 'website_config', 'obsidian_plugin']  })
    type: string

    @Column({nullable: false})
    content: string

    @Column({ nullable: false, default: true })
    status: boolean;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}