import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityCondition } from 'src/utils/types/entity-condition.type';
import { IPaginationOptions } from 'src/utils/types/pagination-options';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { UserDetail } from './entities/user-detail.entity';

@Injectable()
export class UsersDetailService {
    constructor(
        @InjectRepository(UserDetail)
        private usersDetailRepository: Repository<UserDetail>,
    ) { }

    async findOne(fields: EntityCondition<UserDetail>) {
        return await this.usersDetailRepository.findOne({
            where: fields,
        });
    }

    update(id: string, updateProfileDto: {
        unionid?: string,
        externalId?: string
    }) {
        return this.usersDetailRepository.update(id, {
            ...updateProfileDto,
        })
    }

    updateExternalId(id: string, externalId: string) {
        return this.usersDetailRepository.update(id, { externalId });
    }
    updateUnionid(id: string, unionid: string) {
        return this.usersDetailRepository.update(id, { unionid });
    }

    async softDelete(id: string): Promise<void> {
        await this.usersDetailRepository.softDelete(id);
    }
}