import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsPhoneNumber } from 'class-validator';
import { Transform } from 'class-transformer';

export class AuthSendCaptcharDto {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsEmail()
    @IsOptional()
    email?: string | null;

    @ApiProperty({ example: '14998783019' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsPhoneNumber('CN')
    @IsOptional()
    phone?: string | null;
}
