/**
 * 获取公众号API access_token 返回结果封装
 * 
 * Result of getting official account access_token
 * 
 */
export interface AccountAccessTokenResult {
  /**
   * access_token
   */
  access_token: string;
  /**
   * access_token 有效期
   * seconde that access_token will expires in
   */
  expires_in: number;
  /**
   * refresh_token
   */
  refresh_token: string;
  /**
   * openid
   */
  openid: string;
  /**
   * scope
   */
  scope: string;
  /**
   * unionid
   */
  unionid: string;
  /**
   * 正确返回没有该字段
   * There is no this property when success.
   */
  errcode?: string;
  /**
   * 正确返回没有该字段
   * There is no this property when success.
   */
  errmsg?: string;
}
export type WechatMessageType = "text" | "image" | "voice" | "video" | "shortvideo" | "location" | "link" | "event"

/**
 * 微信回调给开发者的消息
 * https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_standard_messages.html
 */
export interface WechatMessage {
  /** 开发者微信号  */
  ToUserName: string;
  /** 发送方帐号 OpenID */
  FromUserName: string;
  /** 消息创建时间 （整型）e.g.`**********` */
  CreateTime: number;
  /** 消息类型 */
  MsgType: WechatMessageType;
  /** 文本消息内容 text  image voice video shortvideo location link event */
  Content?: string;
  /** 消息id，64位整型 */
  MsgId?: number;
  /** 	消息的数据ID（消息如果来自文章时才有） */
  MsgDataId?: string;
  /** 多图文时第几篇文章，从1开始（消息如果来自文章时才有） */
  Idx?: number;

  /** 图片链接（由系统生成） */
  PicUrl?: string;
  /** 图片消息媒体id，语音消息媒体id,视频消息媒体id 可以调用获取临时素材接口拉取数据。 */
  MediaId?: string;
  /** 语音格式，如amr，speex等 */
  Format?: string;
  /**语音识别结果，UTF8编码 */
  Recognition?: string;
  /**视频消息缩略图的媒体id，可以调用多媒体文件下载接口拉取数据。 */
  ThumbMediaId?: string;
  /**维度 */
  Location_X?: string;
  /**经度 */
  Location_Y?: string;
  /** 地图缩放大小 */
  Scale?: string;
  /** 地理位置信息 */
  Label?: string;

  /** 消息标题 */
  Title?: string;
  /** 消息描述 */
  Description?: string;
  /** 消息链接 */
  Url?: string;

  /** 事件 事件类型，subscribe(订阅)、unsubscribe(取消订阅) subscribe SCAN*/
  Event?: string;
  /**几种事件，意义不同。见网页说明 */
  EventKey?: string;
  /**	几种事件，意义不同。见网页说明 */
  Ticket?:string;

}
export interface ReplayWechatMessageXML {
  xml: ReplayWechatMessage;
}

/** 被动回复微信消息 */
export interface ReplayWechatMessage {
    /** 接收方帐号 OpenID */
    ToUserName: string;
    /** 开发者微信号  */
    FromUserName: string;
    /** 消息创建时间 （整型） */
    CreateTime: number;
    /** text  */
    MsgType: string;
    /** 回复的消息内容（换行：在content中能够换行，微信客户端就支持换行显示）  */
    Content: string;
}