import { MigrationInterface, QueryRunner } from "typeorm";

export class pluginRecordAddId1690384003033 implements MigrationInterface {
    name = 'pluginRecordAddId1690384003033'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`download_records\` ADD \`pluginId\` varchar(255) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`download_records\` DROP COLUMN \`pluginId\``);
    }

}
