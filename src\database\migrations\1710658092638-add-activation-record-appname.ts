import { MigrationInterface, QueryRunner } from "typeorm";

export class addActivationRecordAppname1710658092638 implements MigrationInterface {
    name = 'addActivationRecordAppname1710658092638'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`activation_record\` ADD \`appName\` varchar(255) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`activation_record\` DROP COLUMN \`appName\``);
    }

}
