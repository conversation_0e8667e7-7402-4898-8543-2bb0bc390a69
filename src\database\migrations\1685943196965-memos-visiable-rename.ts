import { MigrationInterface, QueryRunner } from "typeorm";

export class memosVisiableRename1685943196965 implements MigrationInterface {
    name = 'memosVisiableRename1685943196965'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`memo\` CHANGE \`visiable\` \`visibility\` enum ('PRIVATE', 'PROTECT', 'PUBLIC') NOT NULL DEFAULT 'PRIVATE'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`memo\` CHANGE \`visibility\` \`visiable\` enum ('PRIVATE', 'PROTECT', 'PUBLIC') NOT NULL DEFAULT 'PRIVATE'`);
    }

}
