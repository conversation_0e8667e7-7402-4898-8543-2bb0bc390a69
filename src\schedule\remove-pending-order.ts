import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { OrdersService } from 'src/orders/orders.service';


@Injectable()
export class RemovePendingOrderSchedule implements OnModuleInit {
    constructor(
        private readonly schedulerRegistry: SchedulerRegistry,
        private readonly orderService: OrdersService,
    ) {

    }

    onModuleInit() {
        this.schedulerRegistry.addCronJob('removePendingOrder', this.removePendingOrder.bind(this));
    }

    @Cron(CronExpression.EVERY_DAY_AT_5AM)
    // @Cron(CronExpression.EVERY_MINUTE)
    async removePendingOrder() {
        Logger.log('移除搁置的订单', 'RemovePendingOrder');
        try {
            const pendingOrders = await this.orderService.findOrders({ status: 'pending' });
            if (pendingOrders) {
                pendingOrders.forEach(async order => {
                    await this.orderService.hardDeleteOrder(order.id);
                })
            }

        } catch (e) {
            Logger.error(e)
        }
    }

}
