# 论坛代理API

这个模块提供了论坛API的代理服务，用于解决前端直接请求论坛API时遇到的跨域问题。

## 功能特性

- 获取论坛最新话题
- 获取用户创建的话题
- 获取论坛分类（包括子分类）
- 获取论坛标签
- 统一的错误处理和日志记录
- 支持环境变量配置论坛URL

## API端点

### 1. 获取最新话题

```
GET /api/v1/forum/latest?apiKey={your_api_key}&clientId={your_client_id}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "topics": [
      {
        "id": 123,
        "title": "话题标题",
        "created_at": "2023-01-01T00:00:00.000Z",
        // ... 其他话题字段
      }
    ]
  }
}
```

### 2. 获取用户创建的话题

```
GET /api/v1/forum/topics/created-by?apiKey={your_api_key}&clientId={your_client_id}&username={username}
```

### 3. 获取论坛分类

```
GET /api/v1/forum/categories?apiKey={your_api_key}&clientId={your_client_id}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": 1,
        "name": "主分类"
      },
      {
        "id": 2,
        "name": "主分类 > 子分类"
      }
    ]
  }
}
```

### 4. 获取论坛标签

```
GET /api/v1/forum/tags?apiKey={your_api_key}&clientId={your_client_id}
```

## 前端使用示例

将您原来的前端代码修改为：

```javascript
// 原来的代码
// fetch(`${forumUrl}/latest.json`, {
//   headers: {
//     'User-Api-Key': apiKey.value,
//     'User-Api-Client-Id': clientId.value,
//   },
// })

// 修改后的代码
async function fetchForumTopics() {
  if (!apiKey.value || !clientId.value) return;
  loadingTopics.value = true;
  loadingMyTopics.value = true;
  
  try {
    // 获取最新话题
    const response = await fetch(`/api/v1/forum/latest?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();
    if (data.success) {
      latestTopics.value = data.data.topics || [];
    }
  } catch (error) {
    console.error('获取最新话题失败:', error);
  } finally {
    loadingTopics.value = false;
  }

  // 获取我的话题
  if (myForumUsername) {
    try {
      const response = await fetch(`/api/v1/forum/topics/created-by?apiKey=${apiKey.value}&clientId=${clientId.value}&username=${encodeURIComponent(myForumUsername)}`);
      const data = await response.json();
      if (data.success) {
        myTopics.value = data.data.topics || [];
      }
    } catch (error) {
      console.error('获取我的话题失败:', error);
    } finally {
      loadingMyTopics.value = false;
    }
  } else {
    loadingMyTopics.value = false;
  }
}

// 获取分类
async function fetchCategories() {
  try {
    const response = await fetch(`/api/v1/forum/categories?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();
    if (data.success) {
      categories.value = data.data.categories || [];
    }
  } catch (error) {
    console.error('获取分类失败:', error);
  }
}

// 获取标签
async function fetchTags() {
  try {
    const response = await fetch(`/api/v1/forum/tags?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();
    if (data.success) {
      tags.value = data.data.tags || [];
    }
  } catch (error) {
    console.error('获取标签失败:', error);
  }
}
```

## 环境配置

在您的环境变量文件（`.env.dev` 或 `.env.prod`）中添加：

```
FORUM_URL=https://forum.pkmer.cn
```

如果不设置此环境变量，将使用默认值 `https://forum.pkmer.cn`。

## 错误处理

API会返回标准的HTTP状态码：
- 200: 成功
- 400: 请求参数错误
- 500: 服务器内部错误

错误响应格式：
```json
{
  "statusCode": 400,
  "message": "API密钥和客户端ID不能为空",
  "error": "Bad Request"
}
```
