import { Controller, Get, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { GoodsService } from './goods.service';

@ApiTags('Goods')
@Controller({ path: 'goods', version: '1' })
export class GoodsController {
    constructor(private readonly goodsService: GoodsService) { }

    @ApiOperation({summary: '获取所有物品', description: '无限制'})
    @HttpCode(HttpStatus.OK)
    @Get()
    async getGoodsList(){
        return await this.goodsService.findAll()
    }
}