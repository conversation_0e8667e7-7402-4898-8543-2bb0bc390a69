import { MigrationInterface, QueryRunner } from "typeorm";

export class createCheckoutOrderTable1710078188206 implements MigrationInterface {
    name = 'createCheckoutOrderTable1710078188206'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`checkout_order\` (\`id\` varchar(36) NOT NULL, \`creatorId\` varchar(255) NULL, \`goodsId\` varchar(255) NULL, \`goodsName\` varchar(255) NOT NULL, \`goodsCount\` int NOT NULL, \`amount\` float NOT NULL, \`email\` varchar(255) NOT NULL, \`status\` varchar(255) NOT NULL DEFAULT 'pending', \`country\` varchar(255) NOT NULL, \`description\` varchar(255) NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, INDEX \`IDX_f4108691f3e75553f999f88a11\` (\`creatorId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_f4108691f3e75553f999f88a11\` ON \`checkout_order\``);
        await queryRunner.query(`DROP TABLE \`checkout_order\``);
    }

}
