import { MigrationInterface, QueryRunner } from "typeorm";

export class addObsidianPlugin1690205017306 implements MigrationInterface {
    name = 'addObsidianPlugin1690205017306'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`download_records\` (\`id\` varchar(36) NOT NULL, \`created_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`creator\` varchar(255) NULL, \`downloadType\` enum ('ObsidianPlugin', 'ObsidianTheme', 'ObsidianCSS', 'ObsidianScript', 'ZoteroPlugin', 'LogseqPlugin') NOT NULL, INDEX \`IDX_da3fb39a5da819de5541f1e77b\` (\`creator\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`obsidian_plugin\` (\`id\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`repo\` varchar(255) NOT NULL, \`author\` varchar(255) NULL, \`authorAvatar\` varchar(255) NULL, \`banner\` varchar(255) NULL, \`tags\` varchar(255) NULL, \`score\` int NULL, \`description\` text NULL, \`chineseDescription\` text NULL, \`version\` varchar(255) NOT NULL, \`isDesktopOnly\` tinyint NOT NULL DEFAULT 0, \`pluginUpdatedTime\` datetime NOT NULL, \`updatedTime\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`downloadCount\` int NOT NULL DEFAULT '0', \`pkmerDownloadCount\` int NOT NULL DEFAULT '0', \`downloadUrl\` varchar(255) NOT NULL, \`source\` enum ('official', 'community') NOT NULL DEFAULT 'official', INDEX \`IDX_aa1298c772dbcbc8bcf161d6fd\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_aa1298c772dbcbc8bcf161d6fd\` ON \`obsidian_plugin\``);
        await queryRunner.query(`DROP TABLE \`obsidian_plugin\``);
        await queryRunner.query(`DROP INDEX \`IDX_da3fb39a5da819de5541f1e77b\` ON \`download_records\``);
        await queryRunner.query(`DROP TABLE \`download_records\``);
    }

}
