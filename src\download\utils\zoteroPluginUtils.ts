import { fetchWithRetry } from "./obsidianPluginUtils";

const PROXY_URL = 'https://amazing.pkmer.cn/proxy/'
export const LATEST_ZOTERO_PLUGIN_LIST_URL = 'https://raw.githubusercontent.com/northword/ZoteroPlugins/main/sources.json'

export interface ZoteroPluginInfo {
    id: string;
    name: string;
    author: string;
    repo: string
    version: string;
    description: string;
    tags: string[];
    downloadCount: number;
    xpiDownloadUrl: string;
    filename: string;
    updatedAt: Date;
}

export interface ZoteroPluginDetail {
    id: string
    name: string;
    repo: string;
    author: string;
    authorAvatar: string;
    banner?: string;
    tags: string[];
    description: string;
    version: string;
    downloadUrl: string;
    downloadCount?: number;
    xpiDownloadUrl?: string;
    updatedAt?: Date;
}

export async function getZoteroPluginList(): Promise<ZoteroPluginInfo[]> {
    const res = await fetchWithRetry(LATEST_ZOTERO_PLUGIN_LIST_URL)
    return await res.json() as ZoteroPluginInfo[]
}

export async function getZoteroReleaseByVersion(zoteroPluginInfo: ZoteroPluginInfo, version: string): Promise<ZoteroPluginDetail> {
    const repo = zoteroPluginInfo.repo.replace("https://github.com/", "")
    const res = await fetchWithRetry(`https://api.github.com/repos/${repo}/releases/tags/${version}`)
    const { tag_name, assets, author, published_at } = await res.json()

    if (tag_name !== version) {
        console.log('版本号不匹配', zoteroPluginInfo)
        return null
    }

    let xpiDownloadUrl = ""
    try {
        const xpiAsset = assets.find(asset => asset.name.endsWith('.xpi'))
        xpiDownloadUrl = xpiAsset.browser_download_url
    } catch (e) {
        console.log(repo, version, e)
    }

    return {
        id: zoteroPluginInfo.id,
        name: zoteroPluginInfo.name,
        author: zoteroPluginInfo.author,
        repo: repo,
        tags: zoteroPluginInfo.tags,
        authorAvatar: author.avatar_url ?? '',
        description: zoteroPluginInfo.description ? zoteroPluginInfo.description : '',
        version: zoteroPluginInfo.version,
        downloadCount: zoteroPluginInfo.downloadCount,
        downloadUrl: '',
        xpiDownloadUrl,
        updatedAt: new Date(published_at)
    }
}