import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Website } from './entities/website.entity';
import { Article } from './entities/article.entity';
import { UsersService } from 'src/users/users.service';
import * as jose from 'jose'
import { ActivationRecord } from './entities/activation-record.entity';
import { randomInt } from 'crypto';

@Injectable()
export class WebsiteService {
    constructor(@InjectRepository(Website) private websiteRepository: Repository<Website>,
        @InjectRepository(Article) private articleRepository: Repository<Article>,
        @InjectRepository(ActivationRecord) private activationRecordRepository: Repository<ActivationRecord>,
        private userService: UsersService) {
    }

    async getWebsiteConfig() {
        const websiteConfig = await this.websiteRepository.find({
            where: { type: 'website_config', status: true }
        })
        return websiteConfig
    }

    async getCommunityObsidianPlugins() {
        const plugins = await this.websiteRepository.find({
            where: {
                type: 'obsidian_plugin', status: true
            }
        })

        return plugins
    }

    async removeCommunityObsidianPlugin(pluginRepo: string) {
        return await this.websiteRepository.delete({ content: pluginRepo })
    }

    async getWechatReply() {
        const wechatReply = await this.websiteRepository.find({
            where: { type: 'wechat_reply', status: true }
        })
        return wechatReply
    }

    async getArticleVisitedTimes(id: string, name: string) {
        const article = await this.articleRepository.findOne({ where: { id } })
        if (!article) {
            await this.articleRepository.save(this.articleRepository.create({ id, name, like: 0, subscribe: 0, visited: 1 }))
            return 1
        }
        await this.articleRepository.update(id, { visited: article.visited + 1 })
        return article.visited + 1
    }

    async getArticleInfo(id: string, name: string) {
        const article = await this.articleRepository.findOne({ where: { id } })
        if (!article) {
            await this.articleRepository.save(this.articleRepository.create({ id, name, like: 0, subscribe: 0, visited: 0 }))
            return article
        }
        return article
    }

    async addArticleLike(id: string, name: string, userId: string) {
        const user = await this.userService.findOne({ id: userId })
        const userArticleInfo = user.article ? user.article : {
            like: [],
            subscribe: []
        } as any
        if (userArticleInfo && userArticleInfo.like && userArticleInfo.like.includes(id)) {
            return {
                code: 400,
                message: 'Already liked it'
            }
        }

        userArticleInfo.like.push(id)
        await this.userService.update(userId, { article: userArticleInfo })

        //  更新文章点赞数，如果文章不存在，则新建记录，如果存在，则更新记录
        const article = await this.articleRepository.findOne({ where: { id } })
        if (article) {
            return await this.articleRepository.update(id, { like: article.like + 1 })
        } else {
            return await this.articleRepository.save(this.articleRepository.create({ id, name, like: 1 }))
        }

    }

    async addSubscribe(id: string, name: string, userId: string) {
        const user = await this.userService.findOne({ id: userId })
        const userArticleInfo = user.article ? user.article : {
            like: [],
            subscribe: []
        } as any

        if (userArticleInfo && userArticleInfo.subscribe && userArticleInfo.subscribe.includes(id)) {
            return {
                code: 400,
                message: 'Already collected'
            }
        }

        userArticleInfo.subscribe.push(id)
        await this.userService.update(userId, { article: userArticleInfo })
        const article = await this.articleRepository.findOne({ where: { id } })
        if (article) {
            return await this.articleRepository.update(id, { subscribe: article.subscribe + 1 })
        } else {
            return await this.articleRepository.save(this.articleRepository.create({ id, name, subscribe: 1 }))
        }

    }

    async cancelArticleLike(id: string, userId: string) {
        const user = await this.userService.findOne({ id: userId })
        if (!user.article) {
            return {
                code: 400,
                message: 'Have not liked this article'
            }
        }
        const userArticleInfo = user.article as any

        if (userArticleInfo && userArticleInfo.like && userArticleInfo.like.includes(id)) {
            userArticleInfo.like = userArticleInfo.like.filter((item: string) => item !== id)
            await this.userService.update(userId, { article: userArticleInfo })
            const article = await this.articleRepository.findOne({ where: { id } })
            if (article) {
                return await this.articleRepository.update(id, { like: article.like - 1 })
            }
        } else {
            return {
                code: 400,
                message: 'Have not liked this article'
            }
        }

    }

    async cancelSubscribe(id: string, userId: string) {
        const user = await this.userService.findOne({ id: userId })
        if (!user.article) {
            return {
                code: 400,
                message: 'Have not collected this article'
            }
        }
        const userArticleInfo = user.article as any

        if (userArticleInfo && userArticleInfo.subscribe && userArticleInfo.subscribe.includes(id)) {
            userArticleInfo.subscribe = userArticleInfo.subscribe.filter((item: string) => item !== id)
            await this.userService.update(userId, { article: userArticleInfo })
            const article = await this.articleRepository.findOne({ where: { id } })
            if (article) {
                return await this.articleRepository.update(id, { subscribe: article.subscribe - 1 })
            }
        } else {
            return {
                code: 400,
                message: 'Have not collected this article'
            }
        }

    }

    private async generateActivationCode(userId: string, appId: string) {
        const salt = randomInt(1000)

        const payload = {
            type: "thino",
            iss: "pkmer",
            userId: userId,
            i: salt,
            appId
        };
        const algorithm = "PS256";
        const pkcs8 = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

        const ecPrivateKey = await jose.importPKCS8(pkcs8, algorithm);

        const jws = await new jose.CompactSign(
            new TextEncoder().encode(JSON.stringify(payload))
        )
            .setProtectedHeader({ alg: "RS256" })
            .sign(ecPrivateKey);

        return jws
    }

    async signProductKey(productName: "thino" | "templify", userId: string, signKey: string) {
        const signString = Buffer.from(signKey, "base64").toString("utf-8")
        let signJson = undefined
        try {
            signJson = JSON.parse(signString)
        } catch {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "Invalid appid"
            }
        }
        if (!signJson || !signJson.machineid || !signJson.hostname) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "Invalid appid"
            }
        }
        const appId = signJson.machineid
        const appName = signJson.platform + "-" + signJson.hostname

        //特殊处理安卓设备，只要平台+hostname一致即可生成
        if (signJson.platform === "android") {
            const isActivationKeyExist = await this.activationRecordRepository.find({
                where: {
                    creatorId: userId,
                    type: productName,
                    appName
                }
            })


            if (isActivationKeyExist[0] && isActivationKeyExist[0].code && isActivationKeyExist[0].appid === appId) {
                return {
                    code: HttpStatus.OK,
                    message: "success",
                    payload: {
                        key: isActivationKeyExist[0].code
                    }
                }
            } else if (isActivationKeyExist[0] && isActivationKeyExist[0].code && isActivationKeyExist[0].appid !== appId) {
                const jws = await this.generateActivationCode(userId, appId)
                try {
                    await this.activationRecordRepository.update(isActivationKeyExist[0].id, {
                        appid: appId,
                        code: jws
                    })

                    return {
                        code: HttpStatus.OK,
                        message: "success",
                        payload: {
                            key: jws
                        }
                    }
                } catch (e) {
                    return {
                        code: HttpStatus.INTERNAL_SERVER_ERROR,
                        message: "Internal error in activation process, please feedback to the developer"
                    }
                }
            }
        }


        // 根据机器id判断是否存在记录
        const isActivationKeyExist = await this.activationRecordRepository.find({
            where: {
                creatorId: userId,
                type: productName,
                appid: appId
            }
        })

        if (isActivationKeyExist[0] && isActivationKeyExist[0].code) {
            return {
                code: HttpStatus.OK,
                message: "success",
                payload: {
                    key: isActivationKeyExist[0].code
                }
            }
        }

        const signedRecordCount = await this.activationRecordRepository.count({
            where: {
                creatorId: userId,
                type: productName
            }
        })
        if (signedRecordCount > 5) {
            return {
                code: 400,
                message: productName + "More than 6 devices have been activated. For more information, please refer to the documentation."
            }
        }
        const user = await this.userService.findOne({ id: userId })
        if (!user) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'The user does not exist or the user needs to log in again',
            };
        }
        if (productName === "thino" && !user.thino) {
            return {
                code: 400,
                message: "Thino Pro has not been purchased and cannot obtain the activation code"
            }
        } else if (productName === "templify") {
            return {
                code: 400,
                message: "Templify is not available yet"
            }
        }

        if (!appId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "Invalid appid"
            }
        }


        const jws = await this.generateActivationCode(userId, appId)

        try {

            const newActivationRecord = await this.activationRecordRepository.create({
                type: productName,
                creatorId: userId,
                appid: appId,
                appName,
                code: jws
            })
            await this.activationRecordRepository.insert(newActivationRecord)
            return {
                code: HttpStatus.OK,
                message: "success",
                payload: {
                    key: jws
                }
            }
        } catch (error) {
            return {
                code: HttpStatus.INTERNAL_SERVER_ERROR,
                message: "Internal error in activation process, please feedback to the developer"
            }
        }
    }

    async getActivationRecord(productName: "thino" | "templify", userId: string) {
        const signedRecord = await this.activationRecordRepository.find({
            where: {
                creatorId: userId,
                type: productName
            }
        })

        let shouldDeleteRecord = new Set()
        signedRecord.forEach((targetRecord) => {
            signedRecord.forEach(async (record) => {
                const isTargetAndroidOrIos = targetRecord.appName.split("-")[0] === "android" || targetRecord.appName.split("-")[0] === "ios"
                const isRecordAndroidOrIos = record.appName.split("-")[0] === "android" || record.appName.split("-")[0] === "ios"
                const isSameAppname = targetRecord.appName === record.appName

                const androidFilterCondition = targetRecord.id !== record.id && isTargetAndroidOrIos && isRecordAndroidOrIos && isSameAppname && targetRecord.appid !== record.appid
                if (androidFilterCondition && new Date(targetRecord.createdAt).getTime() > new Date(record.createdAt).getTime()) {
                    shouldDeleteRecord.add(record.id)
                } else if (androidFilterCondition && new Date(targetRecord.createdAt).getTime() < new Date(record.createdAt).getTime()) {
                    shouldDeleteRecord.add(targetRecord.id)
                } else if (androidFilterCondition && new Date(targetRecord.createdAt).getTime() === new Date(record.createdAt).getTime()) {
                    if (!shouldDeleteRecord.has(targetRecord.id) && !shouldDeleteRecord.has(record.id)) {
                        shouldDeleteRecord.add(targetRecord.id)
                    }
                }
            })
        })

        if (shouldDeleteRecord.size < 3) {
            shouldDeleteRecord.forEach(async (recordId) => {
                await this.activationRecordRepository.delete(recordId)
            })
        }

        const filteredSignedRecord = signedRecord.filter((record) => {
            return !shouldDeleteRecord.has(record)
        })

        return {
            code: HttpStatus.OK,
            payload: filteredSignedRecord
        }
    }

    async deleteActivationRecord(appid: string, userId: string) {
        const signedRecord = await this.activationRecordRepository.find({
            where: {
                appid: appid,
                creatorId: userId
            }
        })

        if (!signedRecord || !signedRecord.length) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "This activation code does not exist or the userid is incorrect"
            }
        }

        const isOver6Month = signedRecord.some((record) => {
            const current = new Date()
            const recordCreated = new Date(record.createdAt)
            // 计算两个日期的差值（以毫秒为单位）
            // @ts-ignore
            const differenceInMs = Math.abs(current - recordCreated);

            // 将差值转换为月份
            const differenceInMonths = differenceInMs / (1000 * 60 * 60 * 24 * 30.4375);
            return differenceInMonths > 6
        })

        if (!isOver6Month) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "The product activation code must be activated for at least 1 month"
            }
        }

        try {
            await this.activationRecordRepository.delete(signedRecord[0].id)
            return {
                code: HttpStatus.OK,
                message: ""
            }
        } catch (error) {
            return {
                code: HttpStatus.INTERNAL_SERVER_ERROR,
                message: "Deletion failed, please contact the developer for processing"
            }
        }

    }

}
