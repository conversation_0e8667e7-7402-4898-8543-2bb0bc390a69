import { registerAs } from '@nestjs/config';

export default registerAs('wechatOpen', () => ({
    appId: process.env.WECHAT_OPEN_APPID,
    secret: process.env.WECHAT_OPEN_SECRET,
    mpAppId: process.env.WECHAT_MP_APPID,
    mpSecret: process.env.WECHAT_MP_SECRET,
    mpId: process.env.WECHAT_MP_ID,  // 订阅号开发者微信号
    serviceId: process.env.WECHAT_SERVICE_ID, // 服务号开发者微信号id
    mpToken: process.env.WECHAT_MP_TOKEN, // 微信公众号 token
    subscribeAppId: process.env.WECHAT_SUBSCRIBE_APPID, //微信订阅号
    subscribeSecret: process.env.WECHAT_SUBSCRIBE_SECRET,
    serviceAppId: process.env.WECHAT_SERVICE_APPID, //微信服务号
    serviceSecret: process.env.WECHAT_SERVICE_SECRET,
}));

// WECHAT_OPEN_APPID = wxba93175274237f7e
// WECHAT_OPEN_SECRET = a9e0e61c9ba7773b44637fa042dcc87f
// WECHAT_MP_APPID = wx48c166ce1c09d017
// WECHAT_MP_SECRET = 60c70eafcbe26922e07ff7558aee05ef
// WECHAT_MP_TOKEN = 7WGb4xsnaUw4Y
// WECHAT_SUBSCRIBE_APPID = wx3bed64ab2073af4a
// WECHAT_SUBSCRIBE_SECRET = 1d27fa17e99cc3b27082d6e6a90fdf96
// WECHAT_MP_ID = gh_bccff9369e01