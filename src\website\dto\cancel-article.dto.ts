import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, Validate } from "class-validator";


export class CancelArticleDto {
    @ApiProperty({ example: "20231001111704" })
    @IsNotEmpty()
    id: string;

    @ApiProperty({ example: 'Obsidian 1.5 重磅更新' })
    @IsOptional()
    name: string;

    @ApiProperty({ example: "like" })
    @IsNotEmpty()
    type: "like" | "subscribe";
}