# 论坛跨域问题解决方案

## 问题描述

前端直接请求论坛API时遇到跨域错误：
```
TypeError: fetch failed
```

虽然使用Postman测试 `https://forum.pkmer.net/latest.json` 可以正常返回数据，但在浏览器环境中会被CORS策略阻止。

## 解决方案

通过后端代理的方式解决跨域问题，将原本的直接请求改为通过后端API代理。

### 技术实现

1. **使用 @nestjs/axios 替代原生 fetch**
   - 更好的错误处理
   - 支持超时设置
   - 更适合 Node.js 环境

2. **统一的错误处理机制**
   - 标准化的错误响应格式
   - 详细的日志记录

3. **环境配置支持**
   - 可通过环境变量配置论坛URL
   - 默认使用 `https://forum.pkmer.net`

## API端点对比

### 原始前端请求
```javascript
// 原来的直接请求（会跨域）
fetch(`${forumUrl}/latest.json`, {
  headers: {
    'User-Api-Key': apiKey.value,
    'User-Api-Client-Id': clientId.value,
  },
})
```

### 新的代理请求
```javascript
// 通过后端代理（解决跨域）
fetch(`/api/v1/forum/latest?apiKey=${apiKey.value}&clientId=${clientId.value}`)
```

## 可用的API端点

| 功能 | 端点 | 参数 |
|------|------|------|
| 获取最新话题 | `GET /api/v1/forum/latest` | apiKey, clientId |
| 获取当前用户信息 | `GET /api/v1/forum/current-user` | apiKey, clientId |
| 获取用户话题 | `GET /api/v1/forum/topics/created-by` | apiKey, clientId, username(可选) |
| 获取论坛分类 | `GET /api/v1/forum/categories` | apiKey, clientId |
| 获取论坛标签 | `GET /api/v1/forum/tags` | apiKey, clientId |

## 新增功能：自动获取当前用户

现在 `getTopicsByUser` 支持以下两种使用方式：

1. **指定用户名**：`/api/v1/forum/topics/created-by?apiKey=xxx&clientId=xxx&username=specific_user`
2. **自动获取当前用户**：`/api/v1/forum/topics/created-by?apiKey=xxx&clientId=xxx`（不提供username参数）

当不提供 `username` 参数时，系统会：
1. 调用 `/session/current.json` 获取当前用户信息
2. 提取用户名
3. 使用该用户名获取话题列表

## 前端代码修改示例

### 获取最新话题
```javascript
async function fetchForumTopics() {
  if (!apiKey.value || !clientId.value) return;
  loadingTopics.value = true;
  
  try {
    const response = await fetch(`/api/v1/forum/latest?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();
    
    if (data.success) {
      latestTopics.value = data.data.topics || [];
    }
  } catch (error) {
    console.error('获取最新话题失败:', error);
  } finally {
    loadingTopics.value = false;
  }
}
```

### 获取当前用户信息
```javascript
async function fetchCurrentUser() {
  try {
    const response = await fetch(`/api/v1/forum/current-user?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();

    if (data.success) {
      currentUser.value = data.data.current_user;
      console.log('当前用户:', currentUser.value?.username);
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
  }
}
```

### 获取用户话题（自动使用当前用户）
```javascript
async function fetchMyTopics() {
  loadingMyTopics.value = true;

  try {
    // 不需要指定用户名，自动获取当前用户的话题
    const response = await fetch(`/api/v1/forum/topics/created-by?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();

    if (data.success) {
      myTopics.value = data.data.topics || [];
      console.log(`获取到用户 ${data.data.username} 的话题`);
    }
  } catch (error) {
    console.error('获取我的话题失败:', error);
  } finally {
    loadingMyTopics.value = false;
  }
}
```

### 获取指定用户话题
```javascript
async function fetchUserTopics(username) {
  try {
    const response = await fetch(`/api/v1/forum/topics/created-by?apiKey=${apiKey.value}&clientId=${clientId.value}&username=${encodeURIComponent(username)}`);
    const data = await response.json();

    if (data.success) {
      return data.data.topics || [];
    }
  } catch (error) {
    console.error(`获取用户 ${username} 的话题失败:`, error);
    return [];
  }
}
```

### 获取分类和标签
```javascript
async function fetchCategories() {
  try {
    const response = await fetch(`/api/v1/forum/categories?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();
    
    if (data.success) {
      categories.value = data.data.categories || [];
    }
  } catch (error) {
    console.error('获取分类失败:', error);
  }
}

async function fetchTags() {
  try {
    const response = await fetch(`/api/v1/forum/tags?apiKey=${apiKey.value}&clientId=${clientId.value}`);
    const data = await response.json();
    
    if (data.success) {
      tags.value = data.data.tags || [];
    }
  } catch (error) {
    console.error('获取标签失败:', error);
  }
}
```

## 响应格式

所有API都返回统一的响应格式：

```json
{
  "success": true,
  "data": {
    // 具体数据内容
  }
}
```

错误响应：
```json
{
  "statusCode": 400,
  "message": "错误信息",
  "error": "Bad Request"
}
```

## 环境配置

在 `.env.dev` 或 `.env.prod` 文件中添加：
```
FORUM_URL=https://forum.pkmer.net
```

## 测试

使用提供的测试脚本验证API是否正常工作：
```bash
node test-forum-api.js
```

## 优势

1. **解决跨域问题** - 通过后端代理避免浏览器CORS限制
2. **统一错误处理** - 标准化的错误响应和日志记录
3. **更好的稳定性** - 使用专业的HTTP客户端库
4. **易于维护** - 集中管理论坛API请求逻辑
5. **环境配置** - 支持不同环境使用不同的论坛URL
