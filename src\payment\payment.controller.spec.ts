import { Test, TestingModule } from '@nestjs/testing';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { HttpStatus } from '@nestjs/common';
import { PaymentDto } from './dto/payment.dto';
import { GoodsService } from 'src/goods/goods.service';

jest.mock('./payment.service');
jest.mock('../goods/goods.service');

describe('PaymentController', () => {
    let paymentController: PaymentController;
    let paymentService: PaymentService;
    let goodsService: GoodsService;

    const mockPaymentService = {
        pay: jest.fn(),
        paymentCallback: jest.fn()
    }

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [PaymentController],
            providers: [
                PaymentService,
                GoodsService
            ]
        }).useMocker((token)=>{
            if(token === PaymentService){
                return mockPaymentService
            }
            if(token === GoodsService){
                return mockPaymentService
            }
        }).compile()

        paymentController = module.get<PaymentController>(PaymentController);
        paymentService = module.get<PaymentService>(PaymentService);
    });

    afterEach(() => {
        jest.resetAllMocks();
    });

    it('should be defined', () => {
        jest.spyOn(paymentService, 'pay').mockImplementation(() => Promise.resolve('test_result'))
        expect(paymentController).toBeDefined();
    });

    //   describe('pay', () => {
    //     it('should call paymentService.pay method with correct arguments and return its result', async () => {
    //       const paymentOptions: PaymentDto = {
    //         orderId: 'order_id',
    //         returnURL: 'return_url',
    //       };
    //       const expectedResult = 'test_result';

    //       paymentService.pay = jest.fn().mockResolvedValue(expectedResult);

    //       const result = await controller.pay(paymentOptions);

    //       expect(paymentService.pay).toHaveBeenCalledWith(
    //         paymentOptions.orderId,
    //         paymentOptions.returnURL,
    //       );
    //       expect(result).toBe(expectedResult);
    //     });
    //   });

    //   describe('paymentCallback', () => {
    //     const body = {
    //       out_trade_no: 'order_id',
    //       trade_status: 'TRADE_SUCCESS',
    //     };

    //     it('should call paymentService.handlePaymentSuccess method with correct arguments for TRADE_SUCCESS status', async () => {
    //       service.handlePaymentSuccess = jest.fn().mockResolvedValue(undefined);

    //       await controller.paymentCallback(body);

    //       expect(service.handlePaymentSuccess).toHaveBeenCalledWith(body.out_trade_no);
    //     });

    //     it('should not call paymentService.handlePaymentSuccess method for non TRADE_SUCCESS status', async () => {
    //       body.trade_status = 'TRADE_FAILED';

    //       service.handlePaymentSuccess = jest.fn().mockResolvedValue(undefined);

    //       await controller.paymentCallback(body);

    //       expect(service.handlePaymentSuccess).not.toHaveBeenCalled();
    //     });
    //   });
});

// 写一个测试oaymentservice的单元测试用例

