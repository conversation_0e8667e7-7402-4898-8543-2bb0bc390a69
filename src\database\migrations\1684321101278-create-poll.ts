import { MigrationInterface, QueryRunner } from "typeorm";

export class createPoll1684321101278 implements MigrationInterface {
    name = 'createPoll1684321101278'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`poll_records\` (\`id\` varchar(36) NOT NULL, \`created_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`creator\` varchar(255) NULL, \`poll_id\` varchar(255) NULL, \`poll_result\` varchar(255) NOT NULL, INDEX \`IDX_c7db3608b2a4e3d44154a76858\` (\`creator\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`poll\` (\`id\` varchar(36) NOT NULL, \`created_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updated_ts\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`poll_name\` varchar(255) NOT NULL, \`poll_description\` varchar(255) NULL, \`poll_type\` enum ('SINGLE', 'MULTIPLE') NOT NULL DEFAULT 'SINGLE', \`poll_status\` enum ('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'INACTIVE', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`poll\``);
        await queryRunner.query(`DROP INDEX \`IDX_c7db3608b2a4e3d44154a76858\` ON \`poll_records\``);
        await queryRunner.query(`DROP TABLE \`poll_records\``);
    }

}
