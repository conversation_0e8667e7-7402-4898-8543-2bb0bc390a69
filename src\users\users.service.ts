import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityCondition } from 'src/utils/types/entity-condition.type';
import { IPaginationOptions } from 'src/utils/types/pagination-options';
import { Repository } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { UserDetail } from './entities/user-detail.entity';

@Injectable()
export class UsersService {
    constructor(
        @InjectRepository(User)
        private usersRepository: Repository<User>,
        @InjectRepository(UserDetail)
        private userDetailRepository: Repository<UserDetail>
    ) { }

    create(createProfileDto: CreateUserDto) {
        return this.usersRepository.save(
            this.usersRepository.create(createProfileDto),
        );
    }

    findManyWithPagination(paginationOptions: IPaginationOptions) {
        return this.usersRepository.find({
            skip: (paginationOptions.page - 1) * paginationOptions.limit,
            take: paginationOptions.limit,
        });
    }

    async findOne(fields: EntityCondition<User>) {
        return await this.usersRepository.findOne({
            where: fields,
        });
    }


    async getUserInfo(fields: EntityCondition<User>) {
        const entity = await this.usersRepository.findOne({
            where: fields,
        });
        entity.updatedAt = new Date()
        await this.usersRepository.save(entity);
        return entity;
    }

    update(id: string, updateProfileDto: UpdateUserDto) {
        return this.usersRepository.update(id, {
            ...updateProfileDto,
        })
    }

    changeUserType(id: string, type: string, expirationDate: Date) {
        return this.usersRepository.update(id, {
            type,
            expirationDate,
        })
    }

    addThinoUser(id: string) {
        return this.usersRepository.update(id, { thino: true, })
    }

    addSupportUser(id: string) {
        return this.usersRepository.update(id, { supporter: true, })
    }

    updateUnionid(id: string, unionid: string) {
        return this.usersRepository.update(id, { unionid });
    }

    updatePhone(id: string, phone: string, password: string) {
        return this.usersRepository.update(id, { phone, password });
    }

    updateEmail(id: string, email: string, password: string) {
        return this.usersRepository.update(id, { email, password });
    }

    async unbindWechatUnionId(id: string) {
        try {
            await this.update(id, {
                openid: null,
                unionid: null,
            });

            return await this.userDetailRepository.update({ userId: id }, {
                externalId: null,
                unionid: null
            })
        } catch (error) {
            return error
        }
    }

    async softDelete(id: string): Promise<void> {
        await this.usersRepository.softDelete(id);
    }
}
