import {
    Body,
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Request,
    Post,
    UseGuards,
    Patch,
    Delete,
    SerializeOptions,
    Param,
    Response,
    Res,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AuthLoginDto } from './dto/auth-login.dto';
import { AuthResetPasswordDto } from './dto/auth-reset-password.dto';
import { AuthUpdateDto } from './dto/auth-update.dto';
import { AuthGuard } from '@nestjs/passport';
import { AuthRegisterLoginDto } from './dto/auth-register-login.dto';
import { AuthSendCaptcharDto } from './dto/auth-send-captchar.dto';
import { AuthCheckUserDto } from './dto/auth-check-user.dto';
import { AuthWechatBindDto } from './dto/auth-wechat-bind.dto';
import { JwtService } from '@nestjs/jwt';
import { AuthBindEmailDto } from './dto/auth-bind-email.dto';

@ApiTags('Auth')
@Controller({
    path: 'auth',
    version: '1',
})
export class AuthController {
    constructor(public service: AuthService, private JwtService: JwtService) { }

    @ApiOperation({ summary: '发送验证码', description: "限制43200秒5次" })
    @Post('send/captchar')
    @HttpCode(HttpStatus.OK)
    public async sendCaptchar(@Body() sendCaptchr: AuthSendCaptcharDto) {
        return this.service.sendCaptcher(sendCaptchr)
    }

    @ApiOperation({ summary: '用户注册', description: "无限制" })
    @Post('register')
    @HttpCode(HttpStatus.CREATED)
    async register(@Body() createUserDto: AuthRegisterLoginDto) {
        return this.service.register(createUserDto);
    }


    @ApiOperation({ summary: '用户登录', description: "无限制" })
    @SerializeOptions({
        groups: ['me'],
    })
    @Post('login')
    @HttpCode(HttpStatus.OK)
    public async login(@Body() loginDto: AuthLoginDto, @Res({ passthrough: true }) res) {
        const result = await this.service.validateLogin(loginDto, false);
        // return res.status(result.code).send(result)
        if (result.code != 200) {
            res.status(HttpStatus.BAD_REQUEST)
        }
        return result
    }

    @ApiOperation({ summary: '管理员登录', description: "必须是管理员，登录不了其它的" })
    @SerializeOptions({
        groups: ['me'],
    })
    @Post('admin/login')
    @HttpCode(HttpStatus.OK)
    public async adminLogin(@Body() loginDTO: AuthLoginDto) {
        return this.service.validateLogin(loginDTO, true);
    }



    // @ApiOperation({ summary: '邮箱验证' })
    // @Post('email/confirm')
    // @HttpCode(HttpStatus.OK)
    // async confirmEmail(@Body() confirmEmailDto: AuthConfirmEmailDto) {
    //     return this.service.confirmEmail(confirmEmailDto.hash);
    // }

    // @ApiOperation({ summary: '请求重置密码' })
    // @Post('forgot/password')
    // @HttpCode(HttpStatus.OK)
    // async forgotPassword(@Body() forgotPasswordDto: AuthForgotPasswordDto) {
    //     return this.service.forgotPassword(forgotPasswordDto.email);
    // }

    @ApiOperation({ summary: '重置密码', description: "无限制" })
    @Post('reset/password')
    @HttpCode(HttpStatus.OK)
    async resetPassword(@Body() resetPasswordDto: AuthResetPasswordDto) {
        return this.service.resetPassword(
            resetPasswordDto.password,
            resetPasswordDto.captchar,
            resetPasswordDto.email,
            resetPasswordDto.phone
        );
    }

    @ApiOperation({ summary: '获取用户自己的信息', description: "必须携带token" })
    @ApiBearerAuth()
    @SerializeOptions({
        groups: ['me'],
    })
    @Get('me')
    @UseGuards(AuthGuard('jwt'))
    @HttpCode(HttpStatus.OK)
    public async me(@Request() request) {
        return await this.service.me(request.user);
    }

    @ApiOperation({ summary: '用户更新自己的账号信息', description: "参数可选" })
    @ApiBearerAuth()
    @SerializeOptions({
        groups: ['me'],
    })
    @Patch('me')
    @UseGuards(AuthGuard('jwt'))
    @HttpCode(HttpStatus.OK)
    public async update(@Request() request, @Body() userDto: AuthUpdateDto) {
        return this.service.update(request.user, userDto);
    }

    @ApiOperation({ summary: '用户绑定邮箱', description: "无限制" })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @Post('bind-email')
    @HttpCode(HttpStatus.CREATED)
    async bindEmail(@Request() request, @Body() payload: AuthBindEmailDto) {
        const user = request.user;
        if (!user) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '用户不存在或用户需重新登录',
            };
        }
        console.log(payload,"1223")
        return await this.service.bindEmail(user.id, payload)
    }

    @ApiOperation({ summary: '注销账户', description: "感觉没啥用" })
    @ApiBearerAuth()
    @Delete('me')
    @UseGuards(AuthGuard('jwt'))
    @HttpCode(HttpStatus.OK)
    public async delete(@Request() request) {
        return this.service.softDelete(request.user);
    }

    @ApiOperation({ summary: '微信登录url', description: "获取微信登录URL" })
    @Get('wechat/geturl')
    public async getUrlWechat(@Request() request) {
        const userAgent = request.headers['user-agent'];
        const isWeChatBrowser = userAgent.toLowerCase().indexOf('micromessenger') !== -1;
        return this.service.getWechatLoginUrl(isWeChatBrowser);
    }

    @ApiOperation({ summary: '微信登录获取Token', description: "获取微信登录Token" })
    @Get('wechat/gettoken')
    public async getTokenWechat(@Request() request) {
        const userAgent = request.headers['user-agent'];
        const isWeChatBrowser = userAgent.toLowerCase().indexOf('micromessenger') !== -1;
        return this.service.wechatLogin(request.query.code, isWeChatBrowser);
    }

    @ApiOperation({ summary: '用户绑定微信', description: "可以不携带token，不携带必须要验证码" })
    @Post('wechat/bind')
    @HttpCode(HttpStatus.OK)
    public async bindWechat(@Request() request, @Body() dto: AuthWechatBindDto) {
        let userId = undefined
        const isLogin = request.headers.authorization ? true : false;
        if (isLogin) {

            const token = request.headers.authorization.split(' ')[1];
            //通过token解析获取userid
            userId = await this.JwtService.decode(token)['id'];

        }

        if (!dto.unionid) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: 'unionid不能为空',
            }
        }

        if (!dto.email && !dto.phone) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '邮箱和手机号不能同时为空',
            }
        }

        if (dto.password === "") {
            delete dto.password
        }

        if (!dto.captchar && !userId) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: '未登录用户，验证码不能为空',
            }
        }

        if (!userId) {
            return this.service.bindWechat(dto, false)
        } else {
            return this.service.bindWechat(dto, true, userId)
        }
    }

    @ApiOperation({ summary: '获取用户扫码关注公众号链接', description: "获取用户扫码关注公众号链接" })
    @Get('wechat/getWechatQrcode')
    public async getWechatQrcode() {
        return this.service.getWechatQrcode();
    }

    @ApiOperation({ summary: '检查用户是否注册', description: "检查用户是否注册" })
    @Post('register/check')
    @HttpCode(HttpStatus.OK)
    public async checkUser(@Body() dto: AuthCheckUserDto) {
        return this.service.checkUser(dto)
    }

    @ApiOperation({ summary: 'Pkmer论坛授权', description: "根据oauth2返回token" })
    @Post('pkmer-forum')
    @HttpCode(HttpStatus.OK)
    public async authPkmerForum(@Body() oAuthPayload: any) {
        return await this.service.authPkmerForum(oAuthPayload)
    }
}
