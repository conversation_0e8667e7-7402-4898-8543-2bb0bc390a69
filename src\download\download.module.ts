import { Module, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';

import { DownloadController } from './download.controller';
import { ObsidianPlugin } from './entities/obsidian_plugin.entity';
import { ObsidianPluginService } from './services/obsidian_plugin.service';
import { ObsidianTheme } from './entities/obsidian_theme.entity';
import { ObsidianThemeService } from './services/obsidian_theme.service';
import { ZoteroPlugin } from './entities/zotero_plugin.entity';
import { ZoteroPluginService } from './services/zotero_plugin.service';
import { DownloadService } from './services/download.service';
import { DownloadRecords } from './entities/download_records.entity';
import { UsersModule } from 'src/users/users.module';
import { ObsidianDownloadProcessor } from './processor/obsidian.processor';
import { WebsiteService } from 'src/website/website.service';
import { WebsiteModule } from 'src/website/website.module';
import { RawBodyMiddleware } from '../common/middleware/raw-body.middleware';

@Module({
    imports: [
        TypeOrmModule.forFeature([ObsidianPlugin, ObsidianTheme, ZoteroPlugin, DownloadRecords]),
        UsersModule,
        ConfigModule,
        WebsiteModule,
        BullModule.registerQueue({ name: "obsidian-download" })
    ],
    providers: [ObsidianPluginService, ObsidianThemeService, DownloadService, ZoteroPluginService, ObsidianDownloadProcessor],
    controllers: [DownloadController],
    exports: [DownloadService, ObsidianPluginService, ObsidianThemeService, ZoteroPluginService]
})
export class DownloadModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(RawBodyMiddleware)
            .forRoutes(
                { path: 'download/obsidian/getInstalledPluginsPaginated', method: RequestMethod.POST },
                { path: 'download/obsidian/getOutdatedPluginsPaginated', method: RequestMethod.POST }
            );
    }
}
