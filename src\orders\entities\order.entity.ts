//一个nestjs的订单实体模块，用于typeorm定义数据库
import { EntityHelper } from 'src/utils/entity-helper';
import { Entity, Column, PrimaryGeneratedColumn, Index, DeleteDateColumn, UpdateDateColumn, CreateDateColumn } from 'typeorm';

//定义一个订单实体类，包含订单号，订单名，订单描述，订单价格，订单类型，订单状态，订单创建时间，订单更新时间，订单删除时间，订单创建者id（关联User实体）
@Entity()
export class Order extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    //一个订单对应一个创建者用户，关联User实体
    @Index()
    @Column({ nullable: true, type: 'uuid' })
    creatorId: string;

    @Column({ type: 'uuid' })
    @Index()
    goodsId: string;

    @Column()
    goodsCount: number;

    @Column({type: 'float'})
    amount: number | null;

    // 'pending' | 'paid' | 'refunded' | 'canceled'
    @Column({ default: 'pending' })
    status: string

    @Column({ nullable: true })
    description?: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn()
    deletedAt: Date;
}