import { MigrationInterface, QueryRunner } from "typeorm";

export class addCommentWebsite1715958391379 implements MigrationInterface {
    name = 'addCommentWebsite1715958391379'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`website\` CHANGE \`type\` \`type\` enum ('wechat_reply', 'website_config', 'obsidian_plugin') NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`website\` CHANGE \`type\` \`type\` enum ('wechat_reply', 'website_config') NULL`);
    }

}
