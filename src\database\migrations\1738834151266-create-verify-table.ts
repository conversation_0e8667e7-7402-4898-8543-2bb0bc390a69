import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateVerifyTable1738834151266 implements MigrationInterface {
    name = 'CreateVerifyTable1738834151266'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`verify\` (\`id\` varchar(36) NOT NULL, \`creatorId\` varchar(255) NULL, \`goodsId\` varchar(255) NOT NULL, \`goodsName\` varchar(255) NOT NULL, \`purchased\` tinyint NOT NULL, \`expiredDate\` datetime NOT NULL, \`activateCount\` int NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, INDEX \`IDX_7f62825e16593ab2aa54832a2f\` (\`creatorId\`), INDEX \`IDX_469db109811421fa658bfb1ee2\` (\`goodsId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_469db109811421fa658bfb1ee2\` ON \`verify\``);
        await queryRunner.query(`DROP INDEX \`IDX_7f62825e16593ab2aa54832a2f\` ON \`verify\``);
        await queryRunner.query(`DROP TABLE \`verify\``);
    }

}
