import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PollRecords } from './entities/poll_records.entity';
import { Poll } from './entities/poll.entity';
import { Repository } from 'typeorm';
import { CreatePollRecordDto } from './dto/create-poll-record.dto';
import { User } from 'src/users/entities/user.entity';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';

@Injectable()
export class PollService {
    constructor(
        @InjectRepository(PollRecords)
        private pollRecordsRepository: Repository<PollRecords>,
        @InjectRepository(Poll)
        private pollRepository: Repository<Poll>,
        @InjectRepository(User)
        private userRepository: Repository<User>,
        @InjectRedis()
        private readonly redis: Redis,
    ) { }

    async getAllPolls() {
        return this.pollRepository.find();
    }

    async getPollById(id: string) {
        return this.pollRepository.findOne({
            where: { id },
        });
    }

    async addNewPollRecord(userId: string, createPollRecordDto: CreatePollRecordDto) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            return {
                code: 400,
                message: '用户不存在',
            };
        }

        //使用redis来存储用户投票的次数，如果是insider用户，那么在一周可以投票三次，如果是user用户，那么在一周可以投票一次
        const pollRecordKey = `poll:${userId}-${createPollRecordDto.poll_id}`;
        const pollRecordValue = await this.redis.get(pollRecordKey);

        if (!pollRecordValue) {
            await this.redis.set(pollRecordKey, 0, 'EX', 604800);
        }

        const currentPollRecordValue = await this.redis.get(pollRecordKey);

        if (user.type === 'insider' && user.supporter === true) {
            if (parseInt(currentPollRecordValue) >= 4) {
                return {
                    code: 400,
                    message: '您本周的投票次数已经用完了',
                };
            }
        } else if (user.type === 'insider' && user.supporter === false) {
            if (parseInt(currentPollRecordValue) >= 2) {
                return {
                    code: 400,
                    message: '您已经投过票了',
                };
            }
        }
        else {
            if (parseInt(currentPollRecordValue) >= 1) {
                return {
                    code: 400,
                    message: '您已经投过票了',
                };
            }
        }

        await this.redis.incr(pollRecordKey);
        return this.pollRecordsRepository.save(
            this.pollRecordsRepository.create({
                creator: userId,
                ...createPollRecordDto
            }),
        );
    }

    //通过pollId获取不同的投票结果,返回不同结果的投票数量
    async getPollResultByPollId(pollId: string) {
        const poll = await this.pollRepository.findOne({
            where: { id: pollId },
        });
        if (!poll) {
            return {
                code: 400,
                message: '该投票不存在',
            }
        }

        if (poll.poll_status === 'INACTIVE') {
            return {
                code: 400,
                message: '该投票已经关闭',
            }
        }

        if (poll.poll_type === 'SINGLE') {
            const result = await this.pollRecordsRepository
                .createQueryBuilder('poll_records')
                .select('poll_records.poll_result')
                .addSelect('COUNT(poll_records.poll_result)', 'count')
                .where('poll_records.poll_id = :pollId', { pollId })
                .groupBy('poll_records.poll_result')
                .getRawMany();
            return result;
        } else if (poll.poll_type === 'MULTIPLE') {
            //如果投票的类型是多选，首先根据pollId获取投票记录
            const pollRecords = await this.pollRecordsRepository.find({
                where: { poll_id: pollId },
            });

            if (!pollRecords.length) {
                return []
            }
            //然后将投票记录中的poll_result字段取出来，split为数组，将所有数组合并为一个数组，然后统计每个选项的数量,以数组对象{poll_records_poll_result: "A", count: "2"}的形式返回
            const result = pollRecords
                .map((item) => item.poll_result.split(','))
                .reduce((prev, cur) => prev.concat(cur))
                .reduce((prev, cur) => {
                    prev[cur] ? prev[cur]++ : (prev[cur] = 1);
                    return prev;
                }
                    , {});
            const resultArray = Object.keys(result).map((key) => {
                return { poll_records_poll_result: key, count: result[key] };
            }
            );
            return resultArray;

        } else {
            return {
                code: 400,
                message: '该投票类型不存在',
            }
        }
    }

    async getPollRecordsByUserId(userId: string) {
        if (!userId) {
            return {
                code: 400,
                message: '用户不存在,请重新登录',
            };
        }
        // console.log('user', userId)
        return await this.pollRecordsRepository.find({
            where: { creator: userId },
        });
    }

    async getUserPollTimes(userId, pollId: string) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            return {
                code: 400,
                message: '用户不存在,请重新登录',
            };
        }


        const pollRecordKey = `${userId}-poll-${pollId}`;
        const pollRecordValue = await this.redis.get(pollRecordKey);
        // console.log('pollvalue', pollRecordValue, user.type, user.supporter)
        if (!pollRecordValue && user.supporter === true) {
            return 4;
        } else if (!pollRecordValue && user.type === 'insider' && user.supporter === false) {
            return 2;
        } else if (!pollRecordValue && user.type === 'user' && user.supporter === false) {
            return 1;
        } else if (pollRecordValue && user.supporter === true) {
            return 4 - parseInt(pollRecordValue);
        } else if (pollRecordValue && user.type === 'insider' && user.supporter === false) {
            return 2 - parseInt(pollRecordValue);
        } else if (pollRecordValue && user.type === 'user' && user.supporter === false) {
            return 1 - parseInt(pollRecordValue);
        }
    }


    //根据pollId获取投票项目的最近投票的十个不同的用户，并根据用户id返回用户昵称
    async getPollUsersByPollId(pollId: string) {

        const recentTenUser = await this.pollRecordsRepository
            .createQueryBuilder('poll_records')
            .select('poll_records.creator')
            .where('poll_records.poll_id = :pollId', { pollId })
            .orderBy('poll_records.created_ts', 'DESC')
            .limit(10)
            .getRawMany();

        //去除重复的用户id
        const filterdRecentTenUser = recentTenUser.filter((item, index, self) => {
            return self.findIndex((t) => t.poll_records_creator === item.poll_records_creator) === index;
        }
        );

        //根据用户id获取用户昵称
        const users = await Promise.all(
            filterdRecentTenUser.map(async (item) => {
                const user = await this.userRepository.findOne({
                    where: { id: item.poll_records_creator },
                });
                return user.nickName;
            }
            )
        );
        return users;
    }
}
