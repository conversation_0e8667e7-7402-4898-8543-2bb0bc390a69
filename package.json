{"name": "pkmer-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "cross-env NODE_ENV=prod nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "cross-env NODE_ENV=dev nest start", "start:dev": "dotenvx run -f .env.dev -- nest start --watch", "start:debug": "cross-env NODE_ENV=dev nest start --debug --watch", "start:prod": "cross-env NODE_ENV=prod node dist/main", "typeorm": "env-cmd -f .env.prod ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:generate", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:run", "migration:revert": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:revert", "schema:drop": "npm run typeorm -- --dataSource=src/database/data-source.ts schema:drop", "seed:run": "ts-node -r tsconfig-paths/register ./src/database/seeds/run-seed.ts", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@adminjs/express": "^5.1.0", "@adminjs/nestjs": "^5.1.1", "@adminjs/typeorm": "^4.0.0", "@alicloud/dysmsapi20170525": "^2.0.23", "@alicloud/openapi-client": "^0.4.5", "@alicloud/openapi-util": "^0.3.1", "@alicloud/tea-util": "^1.4.5", "@dotenvx/dotenvx": "^1.14.0", "@liaoliaots/nestjs-redis": "^9.0.5", "@nestjs-modules/mailer": "^1.8.1", "@nestjs/axios": "^2.0.0", "@nestjs/bull": "^10.1.0", "@nestjs/common": "^9.3.9", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.3.9", "@nestjs/jwt": "^10.0.2", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.3.9", "@nestjs/schedule": "^3.0.1", "@nestjs/swagger": "^6.2.1", "@nestjs/typeorm": "^9.0.1", "@types/body-parser": "^1.19.2", "@wecom/crypto": "^1.0.1", "adminjs": "^6.8.7", "ali-oss": "^6.17.1", "alipay-sdk": "^3.3.0", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "bull": "^4.12.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dayjs": "^1.11.11", "express-formidable": "^1.2.0", "express-session": "^1.17.3", "fast-xml-parser": "^4.2.2", "ioredis": "^5.3.1", "jose": "^5.2.0", "jszip": "^3.10.1", "multer": "1.4.5-lts.1", "mysql2": "^3.2.0", "nestjs-i18n": "^10.2.6", "nodemailer": "^6.9.1", "passport": "^0.6.0", "passport-anonymous": "^1.0.1", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "typeorm": "^0.3.20", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^9.2.0", "@nestjs/schematics": "^9.0.4", "@nestjs/testing": "^9.3.9", "@types/express": "^4.17.17", "@types/jest": "29.2.4", "@types/node": "18.11.18", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "cross-env": "^7.0.3", "env-cmd": "^10.1.0", "eslint": "^8.35.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "jest": "29.3.1", "prettier": "^2.8.4", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "29.0.3", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "4.1.1", "typescript": "^4.9.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}