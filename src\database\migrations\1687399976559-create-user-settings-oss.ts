import { MigrationInterface, QueryRunner } from "typeorm";

export class createUserSettingsOss1687399976559 implements MigrationInterface {
    name = 'createUserSettingsOss1687399976559'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`oss\` (\`id\` varchar(36) NOT NULL, \`creator\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`path\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`settings\` json NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`settings\``);
        await queryRunner.query(`DROP TABLE \`oss\``);
    }

}
