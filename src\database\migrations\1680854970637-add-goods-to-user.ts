import { MigrationInterface, QueryRunner } from "typeorm";

export class addGoodsToUser1680854970637 implements MigrationInterface {
    name = 'addGoodsToUser1680854970637'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`goods\` ADD \`type\` varchar(255) NOT NULL DEFAULT 'other'`);
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`goods\` varchar(255) NOT NULL DEFAULT ''`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`goods\``);
        await queryRunner.query(`ALTER TABLE \`goods\` DROP COLUMN \`type\``);
    }

}
