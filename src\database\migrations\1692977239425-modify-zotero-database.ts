import { MigrationInterface, QueryRunner } from "typeorm";

export class modifyZoteroDatabase1692977239425 implements MigrationInterface {
    name = 'modifyZoteroDatabase1692977239425'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`zotero_plugin\` (\`id\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`repo\` varchar(255) NOT NULL, \`author\` varchar(255) NULL, \`authorAvatar\` varchar(255) NULL, \`banner\` varchar(255) NULL, \`tags\` varchar(255) NULL, \`score\` int NULL, \`description\` text NULL, \`version\` varchar(255) NOT NULL, \`pluginUpdatedTime\` datetime NULL, \`updatedTime\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`downloadCount\` int NOT NULL DEFAULT '0', \`pkmerDownloadCount\` int NOT NULL DEFAULT '0', \`downloadUrl\` varchar(255) NOT NULL, \`valid\` tinyint NOT NULL DEFAULT 1, INDEX \`IDX_ef70622302cb7c1e5e34faad11\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_ef70622302cb7c1e5e34faad11\` ON \`zotero_plugin\``);
        await queryRunner.query(`DROP TABLE \`zotero_plugin\``);
    }

}
