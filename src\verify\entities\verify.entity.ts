//一个nestjs的订单实体模块，用于typeorm定义数据库
import { EntityHelper } from 'src/utils/entity-helper';
import { Entity, Column, PrimaryGeneratedColumn, Index, DeleteDateColumn, UpdateDateColumn, CreateDateColumn } from 'typeorm';

//定义一个订单实体类，包含订单号，订单名，订单描述，订单价格，订单类型，订单状态，订单创建时间，订单更新时间，订单删除时间，订单创建者id（关联User实体）
@Entity()
export class Verify extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Index()
    @Column({ nullable: true, type: 'uuid' })
    creatorId: string;

    @Column({ type: 'uuid' })
    @Index()
    goodsId: string;

    @Column()
    goodsName: string;

    @Column()
    purchased: boolean;

    @Column()
    expiredDate?: Date | null;

    // 激活次数
    @Column()
    activateCount: number;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn()
    deletedAt: Date;
}