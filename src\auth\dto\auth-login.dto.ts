import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, Validate } from 'class-validator';
import { IsNotExist } from 'src/utils/validators/is-not-exists.validator';
import { IsEmail, IsPhoneNumber } from 'class-validator';
import { Transform } from 'class-transformer';

export class AuthLoginDto {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsEmail()
    @IsOptional()
    email?: string | null;

    @ApiProperty({ example: '14998783019' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsPhoneNumber('CN')
    @IsOptional()
    phone?: string | null

    @ApiProperty()
    @IsNotEmpty()
    password: string;
}
