import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class RawBodyMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    if (req.headers['content-type'] && req.headers['content-type'].includes('text/plain')) {
      let data = '';
      req.on('data', chunk => {
        data += chunk;
      });
      
      req.on('end', () => {
        try {
          if (data) {
            console.log('RawBodyMiddleware 收到 text/plain 请求体:', data);
            const parsedData = JSON.parse(data);
            req.body = parsedData; // 替换请求体
            console.log('RawBodyMiddleware 解析后的JSON:', parsedData);
          }
        } catch (e) {
          console.error('RawBodyMiddleware 解析请求体失败:', e);
        }
        
        // 将原始请求体保存到 request 对象
        (req as any).rawBody = data;
        
        next();
      });
    } else {
      next();
    }
  }
} 