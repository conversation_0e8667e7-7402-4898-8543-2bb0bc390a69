import { HttpException, HttpStatus, Module } from '@nestjs/common';
import { FilesController } from './files.controller';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { diskStorage } from 'multer';
import { randomStringGenerator } from '@nestjs/common/utils/random-string-generator.util';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileEntity } from './entities/file.entity';
import { FilesService } from './files.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([FileEntity]),
        MulterModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                const storages = {
                    local: () =>
                        diskStorage({
                            destination: './files',
                            filename: (request, file, callback) => {
                                callback(
                                    null,
                                    `${randomStringGenerator()}.${file.originalname
                                        .split('.')
                                        .pop()
                                        .toLowerCase()}`,
                                );
                            },
                        }),
                    // s3: () => {
                    //     const s3 = new AWS.S3();
                    //     AWS.config.update({
                    //         accessKeyId: configService.get('file.accessKeyId'),
                    //         secretAccessKey: configService.get(
                    //             'file.secretAccessKey',
                    //         ),
                    //         region: configService.get('file.awsS3Region'),
                    //     });

                    //     return multerS3({
                    //         s3: s3,
                    //         bucket: configService.get(
                    //             'file.awsDefaultS3Bucket',
                    //         ),
                    //         acl: 'public-read',
                    //         contentType: multerS3.AUTO_CONTENT_TYPE,
                    //         key: (request, file, callback) => {
                    //             callback(
                    //                 null,
                    //                 `${randomStringGenerator()}.${file.originalname
                    //                     .split('.')
                    //                     .pop()
                    //                     .toLowerCase()}`,
                    //             );
                    //         },
                    //     });
                    // },
                };

                return {
                    fileFilter: (request, file, callback) => {
                        if (
                            !file.originalname.match(/\.(jpg|jpeg|png|gif)$/i)
                        ) {
                            return callback(
                                new HttpException(
                                    {
                                        status: HttpStatus.UNPROCESSABLE_ENTITY,
                                        errors: {
                                            file: `cantUploadFileType`,
                                        },
                                    },
                                    HttpStatus.UNPROCESSABLE_ENTITY,
                                ),
                                false,
                            );
                        }

                        callback(null, true);
                    },
                    storage: storages[configService.get('file.driver')](),
                    limits: {
                        fileSize: configService.get('file.maxFileSize'),
                    },
                };
            },
        }),
    ],
    controllers: [FilesController],
    providers: [ConfigModule, ConfigService, FilesService],
})
export class FilesModule {}
