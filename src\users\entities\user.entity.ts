import {
    Column,
    AfterLoad,
    CreateDateColumn,
    DeleteDateColumn,
    Entity,
    Index,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
    BeforeInsert,
    BeforeUpdate,
} from 'typeorm';
import { Role } from '../../roles/entities/role.entity';
import { Status } from '../../statuses/entities/status.entity';
import * as bcrypt from 'bcryptjs';
import { EntityHelper } from 'src/utils/entity-helper';
import { AuthProvidersEnum } from 'src/auth/auth-providers.enum';
import { Exclude, Expose } from 'class-transformer';

@Entity()
export class User extends EntityHelper {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ nullable: true })
    @Expose({ groups: ['me', 'admin'] })
    email: string | null;

    @Column({ nullable: true })
    @Exclude({ toPlainOnly: true })
    password: string;

    @Exclude({ toPlainOnly: true })
    public previousPassword: string;

    @AfterLoad()
    public loadPreviousPassword(): void {
        this.previousPassword = this.password;
    }

    @BeforeInsert()
    @BeforeUpdate()
    async setPassword() {
        if (this.previousPassword !== this.password && this.password) {
            try {
                const salt = await bcrypt.genSalt();
                this.password = await bcrypt.hash(this.password, salt);
            } catch (error) {
                // Handle the error here
                console.error(error);
                throw new Error('Failed to hash password');
            }
        }
    }

    @Column({ default: AuthProvidersEnum.email })
    @Expose({ groups: ['me', 'admin'] })
    provider: string;

    @Index()
    @Column({ nullable: true })
    @Exclude({ toPlainOnly: true })
    socialId: string | null;

    @Index()
    @Column({ nullable: true })
    phone: string | null;

    @Index()
    @Column({ nullable: true })
    nickName: string | null;

    @Column({ nullable: true })
    @Exclude({ toPlainOnly: true })
    birthday: string | null;

    @BeforeUpdate()
    public updateUserTypeStatus(): void {
        //这个expirationDate是个字符串，不是Date类型
        if (this.expirationDate && new Date(this.expirationDate) <= new Date()) {
            this.type = 'user';
            this.expirationDate = null;
        } else if (!this.expirationDate) {
            this.type = 'user';
            this.expirationDate = null;
        } else if (this.expirationDate && new Date(this.expirationDate) > new Date()) {
            this.type = 'insider';
        }
    }
    //'user' |'insider'
    @Column({ default: 'user', type: 'enum', enum: ['user', 'insider'] })
    type: string

    @Column({ default: false })
    supporter: boolean

    @Column({ type: 'date', nullable: true })
    expirationDate: Date | null = null;

    @Column({ default: false })
    thino: boolean

    @Column({ default: false })
    abroad: boolean

    @BeforeUpdate()
    public updateThinoWebStatus(): void {
        //这个expirationDate是个字符串，不是Date类型
        if (this.thinoWebExpir && new Date(this.thinoWebExpir) <= new Date()) {
            this.thinoWebExpir = null;
        } else if (!this.thinoWebExpir) {
            this.thinoWebExpir = null;
        }
    }

    @Column({ type: 'date', nullable: true })
    thinoWebExpir: Date | null = null;

    @Column({ default: 0 })
    rewards: number

    //购买的商品列表，用逗号分隔
    @Column({ default: '' })
    goods: string

    @Column({ nullable: true })
    avatar: string | null;

    @ManyToOne(() => Role, {
        eager: true,
    })
    @Exclude({ toPlainOnly: true })
    role?: Role | null;

    @ManyToOne(() => Status, {
        eager: true,
    })
    @Exclude({ toPlainOnly: true })
    status?: Status;

    //用于存放用户的设置
    @Column({ type: 'json', nullable: true })
    settings: string | null;

    @Column({ type: 'json', nullable: true })
    article: string | null;

    @Column({ nullable: true })
    @Index()
    openid?: string | null;

    @Column({ nullable: true })
    subscribeWechat?: boolean | null;

    @Column({ nullable: true })
    @Index()
    unionid?: string | null;

    @Column({ nullable: true })
    @Index()
    thinoOpenId: string | null;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;

    @DeleteDateColumn()
    deletedAt: Date | null;
}
