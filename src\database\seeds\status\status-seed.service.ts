import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Status } from 'src/statuses/entities/status.entity';
import { StatusEnum } from 'src/statuses/statuses.enum';
import { Repository } from 'typeorm';

@Injectable()
export class StatusSeedService {
    constructor(
        @InjectRepository(Status)
        private repository: Repository<Status>,
    ) {}

    async run() {
        const count = await this.repository.count();

        if (count === 0) {
            await this.repository.save([
                this.repository.create({
                    id: StatusEnum.active,
                    name: 'Active',
                }),
                this.repository.create({
                    id: StatusEnum.inactive,
                    name: 'Inactive',
                }),
            ]);
        }
    }
}
