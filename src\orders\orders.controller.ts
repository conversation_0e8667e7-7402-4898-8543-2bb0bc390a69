
import { Body, Controller, DefaultValuePipe, Delete, Get, HttpCode, HttpStatus, Param, ParseIntPipe, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { AuthGuard } from '@nestjs/passport';


@ApiTags('Orders')
@Controller({ path: 'orders', version: '1' })
export class OrdersController {
    constructor(private readonly ordersService: OrdersService) { }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '创建订单', description: '无限制' })
    @Post('create')
    @HttpCode(HttpStatus.CREATED)
    createOrder(@Body() createProfileDto: CreateOrderDto) {
        return this.ordersService.createOrder(createProfileDto);
    }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '删除订单', description: '无限制' })
    @Delete(':id')
    deleteOrder(@Param('id') id: string) {
        return this.ordersService.softDeleteOrder(id);
    }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '修改订单', description: '无限制' })
    @Patch(':id')
    updateOrder(@Param('id') id: string, @Body() updateProfileDto: UpdateOrderDto) {
        return this.ordersService.updateOrder(id, updateProfileDto);
    }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '查询订单', description: '无限制' })
    @Get(':id')
    findOrder(@Param('id') id: string) {
        return this.ordersService.findOrder({ id });
    }

    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: '查询用户所有订单', description: '无限制' })
    @Get('user/:id')
    findUserOrders(
        @Param('id') id: string,
        @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
        @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number) {

        if (limit > 50) {
            limit = 50;
        }

        return this.ordersService.findUserOrders({ page, limit }, id);
    }

    @ApiOperation({ summary: '查询是否购买过ZK库', description: '无限制' })
    @Get('zk/:secret/:user')
    isZKVaultUser(
        @Param('secret') secret: string,
        @Param('user') user: string) {

        if (!secret || !user) {
            return {
                code: HttpStatus.BAD_REQUEST,
                message: "请提供secret和用户邮箱或手机号"
            }
        }

        return this.ordersService.isZKVaultUser(secret, user)
    }
}
