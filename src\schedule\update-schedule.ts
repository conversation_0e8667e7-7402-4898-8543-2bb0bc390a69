import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ObsidianPluginService } from 'src/download/services/obsidian_plugin.service';
import { ObsidianThemeService } from 'src/download/services/obsidian_theme.service';
import { ZoteroPluginService } from 'src/download/services/zotero_plugin.service';

@Injectable()
export class UpdateSchedule implements OnModuleInit {
    constructor(
        private readonly schedulerRegistry: SchedulerRegistry,
        private readonly obsidianPluginService: ObsidianPluginService,
        private readonly obsidianThemeService: ObsidianThemeService,
        private readonly zoteroPluginService: ZoteroPluginService
    ) { }

    onModuleInit() {
        this.schedulerRegistry.addCronJob('updateObsidianPlugins', this.updateObsidianPlugins.bind(this));
        this.schedulerRegistry.addCronJob('updateObsidianThemes', this.updateObsidianThemes.bind(this));
        this.schedulerRegistry.addCronJob('updateZoterPlugins', this.updateZoterPlugins.bind(this));
    }

    @Cron(CronExpression.EVERY_2_HOURS)
    async updateObsidianPlugins() {
        Logger.log('更新插件列表', 'UpdatePlugins');
        try {
            await this.obsidianPluginService.updateObsidianPlugins();
        } catch (e) {
            Logger.error(e)
        }

    }

    @Cron(CronExpression.EVERY_3_HOURS)
    async updateObsidianThemes() {
        Logger.log('更新主题列表', 'UpdateThemes');
        try {
            await this.obsidianThemeService.updateObsidianThemes();
        } catch (e) {
            Logger.error(e)
        }

    }

    @Cron(CronExpression.EVERY_12_HOURS)
    async updateZoterPlugins() {
        Logger.log('更新Zotero插件')
        try {
            await this.zoteroPluginService.updateZoteroPlugins()
        } catch (e) {
            Logger.error("更新Zotero插件错误", e)
        }
    }
}
