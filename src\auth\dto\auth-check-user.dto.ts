import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsPhoneNumber } from 'class-validator';
import { Transform } from 'class-transformer';

export class AuthCheckUserDto {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsEmail()
    @IsOptional()
    email?: string | null;

    @ApiProperty({ example: '14998783019' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsPhoneNumber('CN')
    @IsOptional()
    phone?: string | null;

    //uuid
    @ApiProperty({ example: 'f4b3c8f7-7eac-4a1e-9baa-1a0a0e1e5900' })
    @IsOptional()
    uuid?: string | null;
}
