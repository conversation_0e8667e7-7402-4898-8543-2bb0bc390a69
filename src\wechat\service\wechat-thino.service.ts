import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { AccountAccessTokenResult, WechatMessage, ReplayWechatMessageXML } from '../interfaces/wechat.interface';
import { firstValueFrom } from 'rxjs';
import { UsersService } from 'src/users/users.service';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import Redis from 'ioredis';
import WeChatMsgQueue from '../utils/wechatMsgQueue';
import { JwtService } from '@nestjs/jwt';

// 微信服务号
const THINO_ENDPOINT = "https://api.thino.pkmer.cn/test/"
@Injectable()
export class WechatThinoService {
    public wechatMessageQueue = new WeChatMsgQueue(9999);

    constructor(
        private configService: ConfigService,
        private httpService: HttpService,
        private usersService: UsersService,
        private jwtService: JwtService,
        @InjectRedis() private readonly redisClient: Redis,
    ) { }

    async getWechatLoginUrl(isWeChatBrowser: boolean) {
        if (isWeChatBrowser) {
            const appId = this.configService.get('wechatOpen.mpAppId');
            const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=https://pkmer.cn/products/wechatLogin&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
            return url;
        } else {
            const appId = this.configService.get('wechatOpen.appId');
            const url = `https://open.weixin.qq.com/connect/qrconnect?appid=${appId}&redirect_uri=https://pkmer.cn/products/wechatLogin&response_type=code&scope=snsapi_login&state=STATE#wechat_redirect`
            return url;
        }

    }

    async getAccessToken(code: string, isWeChatBrowser: boolean): Promise<AccountAccessTokenResult> {
        const appId = isWeChatBrowser ? this.configService.get('wechatOpen.mpAppId') : this.configService.get('wechatOpen.appId');
        const secret = isWeChatBrowser ? this.configService.get('wechatOpen.mpSecret') : this.configService.get('wechatOpen.secret');
        const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${secret}&code=${code}&grant_type=authorization_code`;
        const { data } = await firstValueFrom(
            this.httpService.get<AccountAccessTokenResult>(url).pipe(),
        );
        return data;
        //         return {
        //   access_token: '68_-IR5xoS24R9EmM9eR7gq-rAKyGhPmT8O8Us_gs7kewTIlWgQ6qM44AqSxxRO-Q0Z9_Q0X0iLBw6sG5TrUk5iiFkYZ26xxH_48TS6GCFdIZ8',
        //   expires_in: 7200,
        //   refresh_token: '68_0JPLngUbTfT4sHauZ4IAAY-8jbu4RoJZAjECW0S--drH4JhKvDlisO-D8zWIQLmr3I70bHNZOM6RT7SikE7AsRmEVbDeTHbAO7iPyCPNj2o',
        //   openid: 'ojsHb6fnO2u1M7MgAaSTImQi5dTc',
        //   scope: 'snsapi_login',
        //   unionid: 'oEkT66YQrhdV5_01iH6CZjrVTtTw'
        // }
    }

    async getUserBindServiceUrl(userId: string): Promise<string> {
        let accessToken = await this.redisClient.get('wechatServiceAccessToken');
        if (!accessToken) {
            const getAccessToken = await fetch(`https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${this.configService.get('wechatOpen.serviceAppId')}&secret=${this.configService.get('wechatOpen.serviceSecret')}`, {
                method: 'GET'
            });

            const getAccessTokenResult = await getAccessToken.json();
            accessToken = getAccessTokenResult.access_token;
            await this.redisClient.set('wechatServiceAccessToken', accessToken, 'EX', 7200);
        }
        let ticket = undefined;
        try {
            const getTikectUrl = `https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=${accessToken}`;
            const getTikect = await fetch(getTikectUrl, {
                method: 'POST',
                body: JSON.stringify({
                    "expire_seconds": 604800,
                    "action_name": "QR_STR_SCENE",
                    "action_info": {
                        "scene": {
                            "scene_str": userId
                        }
                    }
                })
            })
            const getTikectResult = await getTikect.json();
            ticket = getTikectResult.ticket;

        } catch (e) {
            console.log("test", accessToken, this.configService.get('wechatOpen.serviceAppId'), this.configService.get('wechatOpen.serviceSecret'))
            throw new BadRequestException("获取 ticket 失败，请稍后重试");
        }

        if (!ticket) {
            return "获取 ticket 失败，请稍后重试"
        }

        return `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${ticket}`;
    }


    /**
     * 微信公众号 Access token
     * 有效期 2h
     * https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Get_access_token.html
     */
    async getWechatMpAccessToken(): Promise<AccountAccessTokenResult> {
        // TODO: 保存，只有过期时请求
        const appId = this.configService.get('wechatOpen.appId');
        const secret = this.configService.get('wechatOpen.secret');
        const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${secret}`;
        const { data } = await firstValueFrom(
            this.httpService.get<AccountAccessTokenResult>(url).pipe(),
        );
        return data;
        // {"access_token":"ACCESS_TOKEN","expires_in":7200}
        // {"errcode":40013,"errmsg":"invalid appid"}
    }

    private async handleUserSubscribeEvent(message: WechatMessage) {
        //{
        //    ToUserName: 'gh_3418cd4d8b7f',
        //    FromUserName: 'oilo_5tFH3t6NhsIq8Na43h7E37U',
        //    CreateTime: **********,
        //    MsgType: 'event',
        //    Event: 'subscribe',
        //    EventKey: 'qrscene_9addaa3e-d4df-4472-a260-b9b7f3b40e4a',
        //    Ticket: 'gQGs8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyRGpna3BHYXFjX0MxQktNYU5CY0QAAgTuNYFlAwSAOgkA'
        // }
        const userId = message.EventKey.replace("qrscene_", "");
        const user = await this.usersService.findOne({
            id: userId
        })
        if (!user) {
            return "用户不存在，请先到 https://pkmer.cn 个人中心注册才能使用该服务号发送 thino";
        }
        try {
            await this.usersService.update(user.id, {
                thinoOpenId: message.FromUserName
            });
            return "绑定成功！现在你可以发送对应的thino到聊天框了！"
        } catch (e) {
            throw new Error("绑定thino失败" + e)
        }
    }

    // 开发者需要对用户消息在5秒内立即做出回应
    async wechatMpMessageService(message: WechatMessage): Promise<ReplayWechatMessageXML> {
        // 压入处理队列
        this.wechatMessageQueue.enqueue(message);
        this.weChatMsgQueueConsumer(); // 触发消费队列

        const user = await this.usersService.findOne({
            thinoOpenId: message.FromUserName
        })

        let reply = "";
        // 同步 回复
        switch (message.MsgType) {
            case "text":
                if (!user) {
                    reply = "您的 pkmer 账号未绑定此服务号，请先到 https://pkmer.cn 个人中心绑定账号。"
                    return {
                        xml: {
                            ToUserName: message.FromUserName, // 来自谁 Openid
                            FromUserName: this.configService.get('wechatOpen.serviceId'), // 开发者微信号
                            CreateTime: Math.floor(new Date().getTime() / 1000),
                            MsgType: 'text',
                            Content: reply
                        }
                    };
                }
                if (message.Content) {
                    // if (!user.thinoWebExpir || new Date(user.thinoWebExpir) <= new Date()) {
                    //     reply = "您的 thino 云同步功能已过期，请到 https://pkmer.cn 个人中心续费。"
                    //     break;
                    // }
                    //如果messge.Content包含链接，则将链接转为[website name](link)的markdown格式

                    const thinoPayload = {
                        content: message.Content + " #收集/微信",
                        tags: ["收集/微信"]
                    }

                    const createThinoUrl = THINO_ENDPOINT + 'thino/createThino';
                    let token = await this.redisClient.get(`token:${user.id}`);

                    if (!token) {
                        token = await this.jwtService.sign({
                            id: user.id,
                            thino: user.thino,
                            type: user.type,
                        });
                        await this.redisClient.set(`token:${user.id}`, token, 'EX', 2592000);
                    }

                    try {
                        const thinoRes = await fetch(createThinoUrl, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer " + token,
                            },
                            body: JSON.stringify(thinoPayload)
                        });

                        const thinoData = await thinoRes.json();
                        if (thinoData.code !== 200) {
                            reply = "发送 thino 失败，请稍后重试"
                            break;
                        }
                    } catch (e) {
                        await this.redisClient.del(`token:${user.id}`);
                        reply = "发送 thino 失败，请稍后重试"
                        break;
                    }

                    reply = "保存 thino 成功！"
                    break;
                }
                break;
            case "image":
                //下载给定的图片链接，转成Buffer类型，保存到图床并返回链接
                const imageLink = message.PicUrl + '.jpeg';
                const form = new FormData()
                try {
                    const imageRes = await fetch(imageLink);
                    //读取blob，设置boundary
                    const imageBlob = await imageRes.blob();
                    form.append('file', imageBlob, "image.jpeg")

                } catch (e) {
                    reply = "发送 thino 失败，微信服务器异常，请稍后重试" + e
                    break;
                }
                const uploadThinoAssetUrl = THINO_ENDPOINT + 'asset/uploadAsset';
                let token = await this.redisClient.get(`token:${user.id}`);

                if (!token) {
                    token = await this.jwtService.sign({
                        id: user.id,
                        thino: user.thino,
                        type: user.type,
                    });
                    await this.redisClient.set(`token:${user.id}`, token, 'EX', 2592000);
                }

                try {
                    const uploadThinoAssetRes = await fetch(uploadThinoAssetUrl, {
                        method: "POST",
                        headers: {
                            "Authorization": "Bearer " + token,
                        },
                        body: form
                    });
                    const uploadThinoAssetData = await uploadThinoAssetRes.json();
                    if (uploadThinoAssetData.code !== 200) {
                        reply = "发送 thino 失败，请稍后重试"
                        break;
                    }

                    if (uploadThinoAssetData.payload.url) {
                        const createThinoUrl = THINO_ENDPOINT + 'thino/createThino';
                        const thinoRes = await fetch(createThinoUrl, {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                "Authorization": "Bearer " + token,
                            },
                            body: JSON.stringify({
                                content: `![](${uploadThinoAssetData.payload.url})` + " #收集/微信",
                                tags: ["收集/微信"]
                            })
                        });

                        const thinoData = await thinoRes.json();
                        if (thinoData.code !== 200) {
                            reply = "创建 thino 失败，请稍后重试"
                            break;
                        }
                    }
                    reply = uploadThinoAssetData.payload.url;
                } catch (e) {
                    await this.redisClient.del(`token:${user.id}`);
                    reply = "发送 thino 失败，请稍后重试" + e
                    break;
                }

                reply = "保存 thino 成功！";
                break;
            case "voice":
                reply = message.Recognition; // 需要开启语音识别
                break;
            case "video":
                reply = "不支持的消息类型！"
                break;
            case "shortvideo":
                reply = "不支持的消息类型！"
                break;
            case "location":
                break;
            case "link":
                reply = message.Url;
                break;
            case "event":
                // 订阅公众号事件处理 
                if (message.Event === "subscribe") {
                    try {
                        reply = await this.handleUserSubscribeEvent(message);
                    } catch (e) {
                        console.log('订阅公众号事件处理错误', e);
                        reply = "绑定失败，请联系管理员"
                    }
                }
                if (message.Event === "SCAN") {
                    try {
                        reply = await this.handleUserSubscribeEvent(message);
                    } catch (e) {
                        console.log('订阅公众号事件处理错误', e);
                        reply = "绑定失败，请联系管理员"
                    }
                }
                break;
            default:
                reply = "";
                break;
        }
        // 返回文字类型
        return {
            xml: {
                ToUserName: message.FromUserName, // 来自谁 Openid
                FromUserName: this.configService.get('wechatOpen.serviceId'), // 开发者微信号
                CreateTime: Math.floor(new Date().getTime() / 1000),
                MsgType: 'text',
                Content: reply
            }
        };
    }

    async weChatMsgQueueConsumer() {
        // example
        setTimeout(() => {
            this.wechatMessageQueue.dequeue();
            // let item = this.wechatMessageQueue.dequeue();
            // console.log(item);
        }, 5000);
    }
}
