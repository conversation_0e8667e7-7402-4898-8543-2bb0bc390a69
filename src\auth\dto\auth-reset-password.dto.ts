import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { IsEmail, IsPhoneN<PERSON>ber, MinLength, Validate } from 'class-validator';
import { Transform } from 'class-transformer';
import { IsNotExist } from 'src/utils/validators/is-not-exists.validator';

export class AuthResetPasswordDto {
    @ApiProperty({ example: '<EMAIL>' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsEmail()
    @IsOptional()
    email?: string;

    @ApiProperty({ example: '14998783019' })
    @Transform(({ value }) => value.toLowerCase().trim())
    @IsPhoneNumber('CN')
    @IsOptional()
    phone?: string

    @ApiProperty()
    @IsNotEmpty()
    @MinLength(6)
    password: string;

    @ApiProperty()
    @IsNotEmpty()
    captchar: string;
}
