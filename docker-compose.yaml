services:
  database:
    image: mysql:8
    ports:
      - ${DATABASE_PORT}:3306
    volumes:
      - ./data/database:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DATABASE_PASSWORD}
      MYSQL_DATABASE: ${DATABASE_NAME}
    restart: always
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - ${APP_PORT}:${APP_PORT}
    extra_hosts:
      - "host.docker.internal:host-gateway"
