import { Controller, Get, HttpCode, HttpStatus, Query, Headers, BadRequestException } from '@nestjs/common';
import { ApiOperation, ApiTags, ApiQuery } from '@nestjs/swagger';
import { ForumService } from './forum.service';

@ApiTags('Forum')
@Controller({ path: 'forum', version: '1' })
export class ForumController {
    constructor(private readonly forumService: ForumService) { }

    @ApiOperation({ summary: '获取论坛最新话题', description: '' })
    @ApiQuery({ name: 'apiKey', description: '论坛API密钥', required: true })
    @ApiQuery({ name: 'clientId', description: '论坛客户端ID', required: true })
    @Get('latest')
    @HttpCode(HttpStatus.OK)
    async getLatestTopics(
        @Query('apiKey') apiKey: string,
        @Query('clientId') clientId: string,
    ) {
        if (!apiKey || !clientId) {
            throw new BadRequestException('API密钥和客户端ID不能为空');
        }

        return this.forumService.getLatestTopics(apiKey, clientId);
    }

    @ApiOperation({ summary: '获取用户创建的话题', description: '' })
    @ApiQuery({ name: 'apiKey', description: '论坛API密钥', required: true })
    @ApiQuery({ name: 'clientId', description: '论坛客户端ID', required: true })
    @Get('topics/created-by')
    @HttpCode(HttpStatus.OK)
    async getTopicsByUser(
        @Query('apiKey') apiKey: string,
        @Query('clientId') clientId: string,
        @Query('username') username: string,
    ) {
        if (!apiKey || !clientId || !username) {
            throw new BadRequestException('API密钥、客户端ID和用户名不能为空');
        }

        return this.forumService.getTopicsByUser(apiKey, clientId, username);
    }

    @ApiOperation({ summary: '获取论坛分类', description: '' })
    @ApiQuery({ name: 'apiKey', description: '论坛API密钥', required: true })
    @ApiQuery({ name: 'clientId', description: '论坛客户端ID', required: true })
    @Get('categories')
    @HttpCode(HttpStatus.OK)
    async getCategories(
        @Query('apiKey') apiKey: string,
        @Query('clientId') clientId: string,
    ) {
        if (!apiKey || !clientId) {
            throw new BadRequestException('API密钥和客户端ID不能为空');
        }

        return this.forumService.getCategories(apiKey, clientId);
    }

    @ApiOperation({ summary: '获取论坛标签', description: '' })
    @ApiQuery({ name: 'apiKey', description: '论坛API密钥', required: true })
    @ApiQuery({ name: 'clientId', description: '论坛客户端ID', required: true })
    @Get('tags')
    @HttpCode(HttpStatus.OK)
    async getTags(
        @Query('apiKey') apiKey: string,
        @Query('clientId') clientId: string,
    ) {
        if (!apiKey || !clientId) {
            throw new BadRequestException('API密钥和客户端ID不能为空');
        }

        return this.forumService.getTags(apiKey, clientId);
    }
}
