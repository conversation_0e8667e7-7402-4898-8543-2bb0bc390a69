import { MigrationInterface, QueryRunner } from "typeorm";

export class addAboardRemoveMemos1705757957612 implements MigrationInterface {
    name = 'addAboardRemoveMemos1705757957612'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` ADD \`abroad\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user\` DROP COLUMN \`abroad\``);
    }

}
