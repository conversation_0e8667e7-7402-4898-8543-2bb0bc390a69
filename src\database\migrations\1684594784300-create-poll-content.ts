import { MigrationInterface, QueryRunner } from "typeorm";

export class createPollContent1684594784300 implements MigrationInterface {
    name = 'createPollContent1684594784300'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`poll\` ADD \`poll_content\` longtext NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`poll\` DROP COLUMN \`poll_content\``);
    }

}
