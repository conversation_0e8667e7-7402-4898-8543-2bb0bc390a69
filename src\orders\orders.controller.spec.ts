import { Test, TestingModule } from '@nestjs/testing';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { HttpCode, HttpStatus } from '@nestjs/common';

describe('OrdersController', () => {
    let controller: OrdersController;
    let ordersService: OrdersService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [OrdersController],
            providers: [OrdersService],
        }).compile();

        controller = module.get<OrdersController>(OrdersController);
        ordersService = module.get<OrdersService>(OrdersService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('createOrder', () => {
        it('should create order successfully', async () => {
            const createOrderDto: CreateOrderDto = {
                creatorId: '8e4b02e6-5932-4c66-bb9c-8437d4780777',
                description: 'test',
                goodsId: '8e4b02e6-5932-4c66-bb9c-8437d4780777',
                goodsCount: 1,
            };

            jest.spyOn(ordersService, 'createOrder').mockResolvedValue(null);

            const result = await controller.createOrder(createOrderDto);

            expect(result).toEqual({ id: '123' });
            expect(ordersService.createOrder).toHaveBeenCalledWith(createOrderDto);
        });
    });

    // describe('deleteOrder', () => {
    //     it('should delete order successfully', async () => {
    //         const orderId = '123';

    //         jest.spyOn(ordersService, 'deleteOrder').mockResolvedValue(null);

    //         const result = await controller.deleteOrder(orderId);

    //         expect(result).toBe(null);
    //         expect(ordersService.deleteOrder).toHaveBeenCalledWith(orderId);
    //     });
    // });

    // describe('updateOrder', () => {
    //     it('should update order successfully', async () => {
    //         const orderId = '123';
    //         const updateOrderDto: UpdateOrderDto = {
    //             // ...
    //         };

    //         jest.spyOn(ordersService, 'updateOrder').mockResolvedValue({ id: orderId, ...updateOrderDto });

    //         const result = await controller.updateOrder(orderId, updateOrderDto);

    //         expect(result).toEqual({ id: orderId, ...updateOrderDto });
    //         expect(ordersService.updateOrder).toHaveBeenCalledWith(orderId, updateOrderDto);
    //     });
    // });

    // describe('findOrder', () => {
    //     it('should find order successfully', async () => {
    //         const orderId = '123';

    //         jest.spyOn(ordersService, 'findOrder').mockResolvedValue({ id: orderId });

    //         const result = await controller.findOrder(orderId);

    //         expect(result).toEqual({ id: orderId });
    //         expect(ordersService.findOrder).toHaveBeenCalledWith({ id: orderId });
    //     });
    // });

    // describe('findUserOrders', () => {
    //     it('should find user orders successfully', async () => {
    //         const userId = '456';

    //         jest.spyOn(ordersService, 'findUserOrders').mockResolvedValue([]);

    //         const result = await controller.findUserOrders(userId, 1, 10);

    //         expect(result).toEqual([]);
    //         expect(ordersService.findUserOrders).toHaveBeenCalledWith({ page: 1, limit: 10 }, userId);
    //     });

    //     it('should limit the number of records returned', async () => {
    //         const userId = '456';

    //         jest.spyOn(ordersService, 'findUserOrders').mockResolvedValue([]);

    //         await controller.findUserOrders(userId, 1, 100);

    //         expect(ordersService.findUserOrders).toHaveBeenCalledWith({ page: 1, limit: 50 }, userId);
    //     });
    // });
});