import { MigrationInterface, QueryRunner } from "typeorm";

export class createPluginChineseDescription1689588378706 implements MigrationInterface {
    name = 'createPluginChineseDescription1689588378706'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` ADD \`chineseDescription\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`plugin\` DROP COLUMN \`chineseDescription\``);
    }

}
