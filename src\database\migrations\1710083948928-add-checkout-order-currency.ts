import { MigrationInterface, QueryRunner } from "typeorm";

export class addCheckoutOrderCurrency1710083948928 implements MigrationInterface {
    name = 'addCheckoutOrderCurrency1710083948928'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`checkout_order\` ADD \`checkoutOrderId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`checkout_order\` ADD \`currency\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`checkout_order\` CHANGE \`creatorId\` \`creatorId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`checkout_order\` CHANGE \`goodsId\` \`goodsId\` varchar(255) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`checkout_order\` CHANGE \`goodsId\` \`goodsId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`checkout_order\` CHANGE \`creatorId\` \`creatorId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`checkout_order\` DROP COLUMN \`currency\``);
        await queryRunner.query(`ALTER TABLE \`checkout_order\` DROP COLUMN \`checkoutOrderId\``);
    }

}
